<template>
  <div class="menu-reset-wrapper">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="通用知识库" name="first"></el-tab-pane>
      <el-tab-pane label="项目知识库" name="second"></el-tab-pane>
    </el-tabs>
    <div class="filter_box">
      <el-select
        v-model="sd"
        placeholder="请选择属地"
        size="small"
        style="width: 100px; margin-right: 10px"
        @change="changeSd"
      >
        <el-option
          v-for="item in sdOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-input
        v-model="searchValue"
        placeholder="搜索知识文档"
        suffix-icon="el-icon-search"
        style="flex: 1"
        clearable
      ></el-input>
    </div>
    <el-tree
      ref="elTree"
      :data="processedMenuData"
      :props="treeProps"
      :default-expanded-keys="defaultExpandedKeys"
      :current-node-key="currentNodeKey"
      :filter-node-method="filterNode"
      @node-click="handleNodeClick"
      node-key="treeId"
      highlight-current
      :expand-on-click-node="true"
    >
      <template #default="{ node, data }">
        <div class="tree-node-content">
          <!-- 图标显示 -->
          <template v-if="data.iconType === 'image' && data.icon">
            <img :src="data.icon" class="custom-icon" alt="menu icon" />
          </template>
          <template v-else-if="data.icon">
            <i :class="['custom-icon', data.icon]"></i>
          </template>

          <!-- 节点文本 -->
          <span class="node-label">{{ data.treeName }}</span>
        </div>
      </template>
    </el-tree>

    <!-- 分类设置弹框 -->
    <el-dialog
      title="分类设置"
      :visible.sync="setDialogVisible"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="category-setting-container">
        <!-- 左侧分类树形结构 -->
        <div class="category-list">
          <div class="category-header">
            <el-select
              v-model="region"
              placeholder="请选择属地"
              style="width: 140px"
              @change="onRegionChange"
            >
              <el-option
                v-for="item in sdOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-button type="primary" size="small" @click="addCategory"
              >新增</el-button
            >
          </div>
          <div class="category-tree-container">
            <el-tree
              ref="categoryTree"
              :data="categoryTreeData"
              :props="categoryTreeProps"
              :default-expanded-keys="defaultExpandedCategoryKeys"
              :current-node-key="currentCategoryNodeKey"
              node-key="id"
              highlight-current
              :expand-on-click-node="false"
              @node-click="handleCategoryNodeClick"
            >
              <template #default="{ data }">
                <div class="category-tree-node">
                  <span class="category-node-label">{{
                    data.treeName || "未命名分类"
                  }}</span>
                  <div class="category-node-actions">
                    <el-button
                      type="text"
                      size="mini"
                      @click.stop="handleCategoryNodeClick(data)"
                      >编辑</el-button
                    >
                    <el-button
                      type="text"
                      size="mini"
                      @click.stop="deleteCategoryNode(data)"
                      >删除</el-button
                    >
                  </div>
                </div>
              </template>
            </el-tree>
          </div>
        </div>

        <!-- 右侧分类详情 -->
        <div class="category-detail">
          <el-form
            ref="categoryForm"
            :model="currentCategory"
            :rules="categoryRules"
            label-width="100px"
            size="small"
          >
            <el-form-item label="一级分类:" prop="parentId">
              <el-select
                v-model="currentCategory.parentId"
                placeholder="请选择一级分类（可选）"
                style="width: 100%"
                clearable
                :disabled="isEdit"
              >
                <el-option
                  v-for="item in flatCategoryOptions"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="分类名称:" prop="libraryTypeName">
              <el-input
                v-model="currentCategory.libraryTypeName"
                placeholder="请输入分类名称"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="显示排序:" prop="displayOrder">
              <el-input-number
                v-model="currentCategory.displayOrder"
                :min="1"
                :max="9999"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="cancelSetDialog">取消</el-button>
            <el-button type="primary" @click="saveCategory">保存</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSd } from "@/api/property/projectManage";
import {
  getLibraryTree,
  getProjectLibraryTree,
  addCategory,
  updateCategory,
  deleteCategory,
} from "@/api/serve/knowledgeBase.js";
export default {
  name: "NavTree",
  props: {
    // 当前激活的菜单项ID
    activeItemId: {
      type: String,
      default: "",
    },
  },

  data() {
    return {
      activeName: "first",
      searchValue: "",
      //属地
      sd: "",
      sdOptions: [],
      // 树组件的配置项
      processedMenuData: [],
      treeProps: {
        children: "children",
        label: "treeName",
      },
      // 当前选中的节点ID
      currentNodeKey: "",
      // 本地过滤文本
      localFilterText: "",
      // 分类设置弹框相关
      setDialogVisible: false,
      isEdit: false,
      categoryTreeData: [], // 分类树形数据
      categoryTreeProps: {
        children: "children",
        label: "treeName",
      },
      currentCategoryNodeKey: "", // 当前选中的分类节点
      defaultExpandedCategoryKeys: [], // 默认展开的分类节点
      region: "",
      selectedCategoryIndex: 0,
      currentCategory: {
        id: null,
        libraryTypeName: "",
        parentId: null,
        region: "",
        displayOrder: null,
      },
      categoryRules: {
        libraryTypeName: [
          { required: true, message: "请输入分类名称", trigger: "blur" },
          {
            min: 1,
            max: 50,
            message: "长度在 1 到 50 个字符",
            trigger: "blur",
          },
        ],
      },
    };
  },

  watch: {
    // 监听激活项ID变化，同步更新选中状态
    activeItemId(newVal) {
      if (newVal) {
        this.setActiveNode(newVal);
      } else {
        this.currentNodeKey = "";
      }
    },

    // 监听过滤文本变化，触发树过滤
    searchValue(newVal) {
      this.localFilterText = newVal.toLowerCase();
      this.$refs.elTree.filter(newVal);
      this.$nextTick(() => {
        this.expandFilteredNodes();
      });
    },

    // 当选中节点变化时，通知父组件
    currentNodeKey(newVal) {
      this.$emit("update:activeItemId", newVal);
    },
  },

  computed: {
    // 默认展开的节点ID集合
    defaultExpandedKeys() {
      return this.collectExpandedKeys(this.processedMenuData);
    },

    // 扁平化的分类选项，用于父分类选择
    flatCategoryOptions() {
      const options = [];
      const flatten = (nodes, prefix = "") => {
        nodes.forEach((node) => {
          const label = prefix + node.treeName;
          options.push({
            id: node.treeId,
            label: label,
          });
        });
      };
      flatten(this.categoryTreeData);
      return options;
    },
  },

  methods: {
    async getListSd() {
      const res = await listSd();
      this.sdOptions = res.data.map((item) => {
        return {
          label: item,
          value: item,
        };
      });
      this.sd = this.sdOptions[0].value;
      this.$emit('treeChangeSd',this.sd)
    },
    async getLibraryTree() {
      const res = await getLibraryTree({ region: this.sd, needLibrary: true });
      if (res.code === 200) {
        this.processedMenuData = res.data || [];
        console.log("获取知识库树成功:", res, this.processedMenuData);
        this.$nextTick(() => {
          this.selectFirstAvailableNode();
        });
      }
    },
    async getQueryTypesByRegions() {
      const res = await getLibraryTree({ region: this.region });
      if (res.code == 200) {
        // 将数据转换为树形结构
        this.categoryTreeData = res.data || [];
        // 设置默认展开的节点
        this.defaultExpandedCategoryKeys = this.collectCategoryExpandedKeys(
          this.categoryTreeData
        );
      }
    },

    async getProjectLibraryTree() {
      const res = await getProjectLibraryTree({
        region: this.sd,
        needLibrary: true,
      });
      if (res.code === 200) {
        this.processedMenuData = res.data || [];
        console.log("获取知识库树成功:", res, this.processedMenuData);
        this.$nextTick(() => {
          this.selectFirstAvailableNode();
        });
      }
    },

    // 收集需要展开的分类节点
    collectCategoryExpandedKeys(items) {
      const keys = [];
      items.forEach((item) => {
        if (item.children && item.children.length) {
          keys.push(item.id);
          keys.push(...this.collectCategoryExpandedKeys(item.children));
        }
      });
      return keys;
    },
    changeSd() {
      this.$emit('treeChangeSd',this.sd)
      if (this.activeName == "first") {
        this.getLibraryTree();
      } else if (this.activeName == "second") {
        this.getProjectLibraryTree();
      }
    },
    handleClick() {
      if (this.activeName == "first") {
        this.getLibraryTree();
      } else if (this.activeName == "second") {
        this.getProjectLibraryTree();
      }
      this.$emit('changeCategory',this.activeName)
    },
    // 节点过滤方法
    filterNode(value, data) {
      if (!value) return true;
      return data.treeName.toLowerCase().includes(value.toLowerCase());
    },

    // 收集需要默认展开的节点ID
    collectExpandedKeys(items) {
      const keys = [];
      items.forEach((item) => {
        // 有子节点的节点需要展开
        if (item.children && item.children.length) {
          keys.push(item.treeId);
          keys.push(...this.collectExpandedKeys(item.children));
        }
      });
      return keys;
    },

    // 展开所有包含过滤结果的节点
    expandFilteredNodes() {
      if (!this.localFilterText) return;

      const expandNodes = (nodes) => {
        nodes.forEach((node) => {
          if (node.children && node.children.length) {
            // 检查是否有子节点匹配过滤条件
            const hasMatchingChild = node.children.some(
              (child) =>
                child.treeName.toLowerCase().includes(this.localFilterText) ||
                (child.children && this.hasMatchingDescendant(child))
            );

            if (
              hasMatchingChild ||
              node.treeName.toLowerCase().includes(this.localFilterText)
            ) {
              expandNodes(node.children);
            }
          }
        });
      };

      expandNodes(this.processedMenuData);
    },

    // 检查是否有后代节点匹配过滤条件
    hasMatchingDescendant(node) {
      if (node.treeName.toLowerCase().includes(this.localFilterText))
        return true;
      if (node.children && node.children.length) {
        return node.children.some((child) => this.hasMatchingDescendant(child));
      }
      return false;
    },

    // 处理节点点击事件
    handleNodeClick(data) {
      // 只有三级节点可以被激活
      if (this.activeName == "first") {
        if (data.level === 3) {
          this.currentNodeKey = data.treeId;
          this.$emit("item-click", data);
        }
      } else if (this.activeName == "second") {
        if (data.level === 2) {
          this.currentNodeKey = data.treeId;
          this.$emit("item-click", data);
        }
      }
    },

    // 选中第一个可选中的节点（三级节点）
    selectFirstAvailableNode() {
      console.log("开始自动选中第一个可选中节点...", this.processedMenuData);
      const firstSelectableNode = this.findFirstSelectableNode(
        this.processedMenuData
      );
      if (firstSelectableNode) {
        console.log(
          "自动选中第一个可选中节点:",
          firstSelectableNode.treeName,
          firstSelectableNode.treeId
        );

        // 先展开父节点
        this.expandParentNodes(firstSelectableNode.treeId);

        // 然后设置当前选中节点
        this.$nextTick(() => {
          this.currentNodeKey = firstSelectableNode.treeId;
          // 确保树组件更新选中状态
          this.$refs.elTree.setCurrentKey(firstSelectableNode.treeId);
          // 触发点击事件
          this.$emit("item-click", firstSelectableNode);
          console.log("节点选中完成，currentNodeKey:", this.currentNodeKey);
        });
      } else {
        console.log("未找到可选中的节点");
      }
    },

    // 递归查找第一个可选中的节点（三级节点）
    findFirstSelectableNode(nodes) {
      for (const node of nodes) {
        // 如果是三级节点，直接返回
        if (this.activeName == "first") {
          if (node.level === 3) {
            return node;
          }
        } else if (this.activeName == "second") {
          if (node.level === 2) {
            return node;
          }
        }

        // 如果有子节点，递归查找
        if (node.children && node.children.length) {
          const found = this.findFirstSelectableNode(node.children);
          if (found) return found;
        }
      }
      return null;
    },

    // 设置激活节点并展开所有父节点
    setActiveNode(nodeId) {
      if (!nodeId) return;

      // 找到节点并设置为当前节点
      const node = this.findNodeById(this.processedMenuData, nodeId);
      if (node && node.level === 3) {
        // 先展开父节点
        this.expandParentNodes(nodeId);

        // 然后设置当前选中节点
        this.$nextTick(() => {
          this.currentNodeKey = nodeId;
          // 确保树组件更新选中状态
          this.$refs.elTree.setCurrentKey(nodeId);
        });
      }
    },

    // 递归查找节点
    findNodeById(nodes, id) {
      for (const node of nodes) {
        if (node.treeId === id) {
          return node;
        }
        if (node.children && node.children.length) {
          const found = this.findNodeById(node.children, id);
          if (found) return found;
        }
      }
      return null;
    },

    // 展开所有父节点
    expandParentNodes(nodeId) {
      this.$nextTick(() => {
        const node = this.$refs.elTree.getNode(nodeId);
        if (node && node.parent) {
          let parent = node.parent;
          while (parent && parent.level > 0) {
            // 展开父节点
            if (!parent.expanded) {
              this.$refs.elTree.expandNode(parent);
            }
            parent = parent.parent;
          }
        }
      });
    },

    // 属地变化处理
    onRegionChange() {
      // 重置表单
      this.$refs.categoryForm.resetFields();
      this.getQueryTypesByRegions();
    },

    // 分类设置相关方法
    async openSetDialog() {
      this.setDialogVisible = true;
      this.region = JSON.parse(JSON.stringify(this.sd));
      await this.getQueryTypesByRegions();
    },

    // 新增分类 - 只初始化表单，不直接添加到树中
    addCategory() {
      this.isEdit = false;
      // 重置表单为新增状态
      this.currentCategory = {
        id: null,
        libraryTypeName: "",
        parentId: this.currentCategoryNodeKey || null,
        displayOrder: null,
        region: this.region,
      };
      this.currentCategoryNodeKey = "";
    },
    // 处理分类树节点点击
    handleCategoryNodeClick(data) {
      this.isEdit = true;
      this.currentCategoryNodeKey = data.treeId;
      this.currentCategory = {
        id: data.treeId || null,
        libraryTypeName: data.treeName || null,
        parentId: data.parentTreeId == 0 ? null : data.parentTreeId,
        displayOrder: data.displayOrder || null,
        region: data.region || null,
      };
    },

    // 删除分类节点
    deleteCategoryNode(categoryData) {
      this.$confirm("确定要删除这个分类吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            const res = await deleteCategory(categoryData.treeId);
            if (res.code === 200) {
              this.$message.success("删除成功");
              // 刷新分类树
              await this.getQueryTypesByRegions();
              // 刷新左侧主树图
              await this.getLibraryTree();
              // 重置当前选中的分类
              this.currentCategoryNodeKey = "";
              this.currentCategory = {
                id: null,
                libraryTypeName: "",
                parentId: null,
                displayOrder: null,
                region: this.region,
              };
            } else {
              this.$message.error(res.msg || "删除失败");
            }
          } catch (error) {
            this.$message.error("删除失败");
            console.error("删除分类失败:", error);
          }
        })
        .catch(() => {
          // 取消删除
        });
    },

    // 保存分类
    async saveCategory() {
      this.$refs.categoryForm.validate(async (valid) => {
        if (valid) {
          try {
            const categoryData = {
              ...this.currentCategory,
              region: this.region,
            };

            let res;
            if (categoryData.id) {
              // 编辑分类
              res = await updateCategory(categoryData);
            } else {
              // 新增分类
              res = await addCategory(categoryData);
            }

            if (res.code === 200) {
              this.$message.success(categoryData.id ? "修改成功" : "新增成功");
              this.setDialogVisible = false;

              // 刷新分类树
              await this.getQueryTypesByRegions();

              // 刷新左侧主树图
              await this.getLibraryTree();

              // 重置表单
              this.currentCategory = {
                id: null,
                libraryTypeName: "",
                parentId: null,
                displayOrder: null,
                region: this.region,
              };
              this.currentCategoryNodeKey = "";
            } else {
              this.$message.error(res.msg || "保存失败");
            }
          } catch (error) {
            this.$message.error("保存失败");
            console.error("保存分类失败:", error);
          }
        } else {
          this.$message.error("请完善分类信息");
          return false;
        }
      });
    },

    // 取消设置
    cancelSetDialog() {
      this.setDialogVisible = false;
      // 重置表单
      this.$refs.categoryForm.resetFields();
    },
  },

  async mounted() {
    await this.getListSd();
    await this.getLibraryTree();
    // 初始化时设置激活节点
    if (this.activeItemId) {
      this.setActiveNode(this.activeItemId);
    }
  },
};
</script>

<style lang="scss">
.menu-reset-wrapper {
  padding: 10px 0;

  /* 节点内容样式 */
  .el-tree-node__content {
    display: flex;
    align-items: center;
    height: 40px !important;
    line-height: 40px !important;
    padding: 0 10px;
    margin: 2px 0;
    border-radius: 5px;
    font-size: 14px !important;
    color: #1d2129;
    transition: all 0.3s ease;

    .el-tree-node__expand-icon {
      position: absolute;
      right: 20px;
    }

    /* 悬停效果 */
    &:hover {
      background-color: #f5f7fa;
    }
  }

  /* 焦点状态样式 */
  .el-tree-node:focus > .el-tree-node__content,
  .el-tree-node__content:focus {
    outline: none;
  }

  /* 图标样式 */
  .custom-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    font-size: 16px !important;
  }
}
.filter_box {
  display: flex;
  align-content: center;
  align-items: center;
  justify-content: space-between;
  .el-input__inner {
    height: 32px;
    line-height: 32px;
  }
}

/* 分类设置弹框样式 */
.category-setting-container {
  display: flex;
  height: 400px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;

  .category-list {
    width: 240px;
    border-right: 1px solid #e4e7ed;

    .category-header {
      padding: 10px;
      border-bottom: 1px solid #e4e7ed;
      background-color: #f5f7fa;
      display: flex;
      align-content: center;
      align-items: center;
      justify-content: space-between;
    }

    .category-tree-container {
      height: calc(100% - 70px);
      overflow-y: auto;
      padding: 10px;

      .category-tree-node {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding-right: 10px;

        .category-node-label {
          flex: 1;
          font-size: 14px;
          color: #333;
        }

        .category-node-actions {
          display: flex;
          gap: 5px;
          opacity: 0;
          transition: opacity 0.3s;

          .el-button--text {
            padding: 0;
            font-size: 12px;
            color: #666;

            &:hover {
              color: #1890ff;
            }
          }
        }

        &:hover .category-node-actions {
          opacity: 1;
        }
      }

      .el-tree-node__content {
        height: 36px;
        line-height: 36px;

        &:hover {
          background-color: #f5f7fa;
        }
      }

      .el-tree-node.is-current > .el-tree-node__content {
        background-color: #e6f7ff;
        color: #1890ff;
      }
    }

    .category-items {
      height: calc(100% - 50px);
      overflow-y: auto;

      .category-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        border-bottom: 1px solid #e4e7ed;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
          background-color: #f0f2f5;
        }

        &.active {
          background-color: #e6f7ff;
          border-right: 2px solid #1890ff;
        }

        .category-name {
          flex: 1;
          font-size: 14px;
          color: #333;
        }

        .category-actions {
          display: flex;
          gap: 5px;

          .el-button--text {
            padding: 0;
            font-size: 12px;
            color: #666;

            &:hover {
              color: #1890ff;
            }
          }
        }
      }
    }
  }

  .category-detail {
    flex: 1;
    padding: 20px;
    background-color: #fff;

    .el-form-item {
      margin-bottom: 20px;
    }

    .el-form-item__label {
      color: #333;
      font-weight: 500;
    }
  }
}

.dialog-footer {
  text-align: center;
  padding-top: 10px;
}
</style>
