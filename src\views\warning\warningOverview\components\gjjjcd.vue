<template>
  <div style="width: 100%; height: 223px" id="gjjjcdfx"></div>
</template>

<script>
import * as echarts from "echarts";
export default {
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    data: {
      handler(val) {
        this.initChart(val);
      },
      deep: true,
    },
  },
  mounted() {},
  methods: {
    initChart(data) {
      let total = 0;
      let resdata = data.map((x) => {
        return {
          name: x.name,
          value: x.num,
        };
      });
      data.forEach((x) => {
        total += parseInt(x.num);
      });
      let chart = echarts.init(document.getElementById("gjjjcdfx"));
      let option = {
        color: ["#0E42D2", "#249EFF", "#21CCFF", "#64EADC", "#86DF6C"],
        title: {
          text: total,
          subtext: "总数",
          top: "28%",
          left: "center",
          textStyle: {
            fontSize: 24,
            lineHeight: 24,
          },
          subtextStyle: {
            fontSize: 16,
          },
        },
        tooltip: {
          trigger: "item",
        },
        legend: {
          right: "12%",
          top: "bottom",
          icon: "circle",
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 18,
          textStyle: {
            padding: [0, 0, 0, 6],
          },
        },
        series: [
          {
            type: "pie",
            radius: ["48%", "70%"],
            center: ["50%", "40%"],
            label: {
              show: true,
              position: "outside", // 将标签放置在扇区外部
              formatter: function(params) {
                // 确保值为0时也显示0%
                return params.percent.toFixed(0) + '%';
              },
            },
            labelLine: {
              show: true,
              length: 10, // 连接线长度
              length2: 14, // 引导线长度
            },
            data: resdata,
          },
        ],
      };
      chart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
