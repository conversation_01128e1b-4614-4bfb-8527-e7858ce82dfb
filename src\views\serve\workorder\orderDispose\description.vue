<template>
  <div class="wrap">
    <div class="line1">
      <div class="item">
        <div class="label">问题描述：</div>
        <div
          class="value"
          v-if="data.describe"
          v-html="formattedDescribe"
        ></div>
        <div v-else class="value">-</div>
      </div>
    </div>
    <!-- <div class="line1">
      <div class="item">
        <div class="label">影响范围：</div>
        <div class="value">{{ data.yxfw || "-" }}</div>
      </div>
    </div>
    <div class="line1">
      <div class="item">
        <div class="label">处理建议：</div>
        <div class="value">{{ data.cljy || "-" }}</div>
      </div>
    </div> -->
    <div class="line1">
      <div class="item">
        <div class="label">附件：</div>
        <div class="file-display-container">
          <div class="fileList flex-c">
            <!-- 处理新的 fileSuffix 和 fileUrl 格式 -->
            <div
              class="file flex-c"
              v-for="(file, i) in processedFileList"
              :key="i"
              @click="previewFile(file, i)"
            >
              <!-- 图片类型显示缩略图 -->
              <img
                v-if="file.type === 4"
                :src="getFileUrl(file.fileName)"
                class="thumbnail-image"
                :alt="file.fileName"
                @error="handleImageError"
              />

              <!-- 其他文件类型显示对应图标 -->
              <img
                v-else
                :src="getFileIcon(file.type)"
                :alt="getFileTypeName(file.type)"
              />

              <div class="filename">
                {{ getDisplayFileName(file.fileName) }}
              </div>
            </div>

            <!-- 兼容旧的 fj 字段格式 -->
            <template v-if="!data.fileSuffix"> 无 </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    /**
     * 处理 fileSuffix 和 fileUrl 字段，转换为文件信息数组
     */
    processedFileList() {
      console.log(
        "processedFileList",
        this.data,
        this.data.fileSuffix,
        this.data.fileUrl
      );
      if (!this.data.fileSuffix || !this.data.fileUrl) {
        return [];
      }
      const suffixArray = this.data.fileSuffix
        .split(",")
        .filter((item) => item.trim());
      const urlArray = this.data.fileUrl
        .split(",")
        .filter((item) => item.trim());

      // 确保两个数组长度一致
      const minLength = Math.min(suffixArray.length, urlArray.length);

      return Array.from({ length: minLength }, (_, index) => {
        const type = parseInt(suffixArray[index]);
        const fileName = urlArray[index];

        return {
          fileName: fileName,
          type: type,
          typeName: this.getFileTypeName(type),
        };
      });
    },
    /**
     * 将\n替换为<br>，用于多行显示描述
     */
    formattedDescribe() {
      if (!this.data.describe) return "";
      // 防止XSS，简单转义（如有更严格需求可用第三方库）
      const escapeHtml = (str) =>
        str.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;");
      return escapeHtml(this.data.describe).replace(/\n/g, "<br>");
    },
  },
  methods: {
    /**
     * 根据文件类型获取对应的图标
     * @param {Number} type - 文件类型：1-文档，2-表格，3-PDF，4-图片
     * @returns {String} 图标路径
     */
    getFileIcon(type) {
      const iconMap = {
        1: require("@/assets/images/serve/word.png"), // 文档
        2: require("@/assets/images/serve/excel.png"), // 表格
        3: require("@/assets/images/serve/pdf.png"), // PDF
        4: "", // 图片类型不使用图标，直接显示缩略图
      };

      return iconMap[type] || require("@/assets/images/serve/word.png");
    },

    /**
     * 根据文件类型获取类型名称
     * @param {Number} type - 文件类型
     * @returns {String} 类型名称
     */
    getFileTypeName(type) {
      const typeNames = {
        1: "文档",
        2: "表格",
        3: "PDF",
        4: "图片",
      };

      return typeNames[type] || "未知";
    },

    /**
     * 获取文件完整URL
     * @param {String} fileName - 文件名
     * @returns {String} 完整的文件URL
     */
    getFileUrl(fileName) {
      if (!fileName) return "";

      // 如果已经是完整URL，直接返回
      if (fileName.startsWith("http")) {
        return fileName;
      }

      // 否则拼接基础URL
      const baseUrl = process.env.VUE_APP_BASE_API;
      return `${baseUrl}/${fileName}`;
    },

    /**
     * 获取显示用的文件名（截断过长的文件名）
     * @param {String} fileName - 文件名
     * @returns {String} 显示用的文件名
     */
    getDisplayFileName(fileName) {
      if (!fileName) return "";

      // 提取文件名部分（去掉路径）
      const name = fileName.split("/").pop() || fileName;

      // 如果文件名过长，进行截断
      if (name.length > 15) {
        const ext = name.split(".").pop();
        const nameWithoutExt = name.substring(0, name.lastIndexOf("."));
        return `${nameWithoutExt.substring(0, 10)}...${ext}`;
      }

      return name;
    },

    /**
     * 预览文件（新格式）
     * @param {Object} file - 文件信息对象
     * @param {Number} index - 文件索引
     */
    previewFile(file, index) {
      const fileUrl = this.getFileUrl(file.fileName);

      if (file.type === 4) {
        // 图片预览
        this.showImagePreview(fileUrl);
      } else {
        // 其他文件类型 - 新窗口打开
        window.open(fileUrl, "_blank");
      }
    },

    /**
     * 处理图片加载错误
     * @param {Event} event - 错误事件
     */
    handleImageError(event) {
      // 图片加载失败时显示默认图标
      event.target.style.display = "none";
      const parent = event.target.parentNode;
      if (parent && !parent.querySelector(".error-icon")) {
        const errorIcon = document.createElement("i");
        errorIcon.className = "el-icon-picture-outline error-icon";
        parent.appendChild(errorIcon);
      }
    },

    /**
     * 获取图片URL（兼容旧方法）
     * @param {String} fileName - 文件名
     * @returns {String} 完整的图片URL
     */
    getImageUrl(fileName) {
      if (!fileName) return "";

      // 如果已经是完整URL，直接返回
      if (fileName.startsWith("http")) {
        return fileName;
      }

      // 否则拼接基础URL
      const baseUrl = process.env.VUE_APP_BASE_API;
      return `${baseUrl}/${fileName}`;
    },

    /**
     * 预览旧格式的文件
     * @param {Object} file - 文件对象
     */
    previewOldFormatFile(file) {
      const fileUrl = this.getImageUrl(file.fileName);

      if (file.type === "image") {
        // 图片预览 - 可以使用 Element UI 的图片预览或自定义预览
        this.showImagePreview(fileUrl);
      } else {
        // 其他文件类型 - 新窗口打开
        window.open(fileUrl, "_blank");
      }
    },

    /**
     * 显示图片预览
     * @param {String} imageUrl - 图片URL
     */
    showImagePreview(imageUrl) {
      // 创建一个简单的图片预览
      const img = new Image();
      img.onload = () => {
        const newWindow = window.open("", "_blank");
        newWindow.document.write(`
          <html>
            <head>
              <title>图片预览</title>
              <style>
                body { margin: 0; padding: 20px; background: #f5f5f5; display: flex; justify-content: center; align-items: center; min-height: 100vh; }
                img { max-width: 100%; max-height: 100%; object-fit: contain; box-shadow: 0 4px 12px rgba(0,0,0,0.15); }
              </style>
            </head>
            <body>
              <img src="${imageUrl}" alt="预览图片" />
            </body>
          </html>
        `);
        newWindow.document.close();
      };
      img.onerror = () => {
        this.$message.error("图片加载失败");
      };
      img.src = imageUrl;
    },
  },
};
</script>

<style lang="scss" scoped>
.line1 {
  width: 80%;
  display: flex;
  align-items: center;
  padding: 10px 14px;
  box-sizing: border-box;
}
.item {
  display: flex;
  .label {
    width: 90px;
    margin-right: 20px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #4e5969;
    line-height: 20px;
    text-align: left;
  }
  .value {
    width: auto;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #1d2129;
    line-height: 20px;
    text-align: left;
    flex: 1;
  }
}
.file-display-container {
  flex: 1;
}

.file {
  margin-right: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px;
  border-radius: 6px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background-color: #f8f9fa;
  }

  img {
    width: 40px;
    height: 40px;
    border-radius: 4px;

    &.thumbnail-image {
      object-fit: cover;
      border: 1px solid #e5e7eb;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
  }

  .filename {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #6b7280;
    line-height: 16px;
    text-align: left;
    margin-left: 10px;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.no-files {
  color: #999;
  font-size: 14px;
  font-style: italic;
}

.error-icon {
  font-size: 32px;
  color: #ccc;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
