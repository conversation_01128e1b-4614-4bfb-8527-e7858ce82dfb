import request from '@/utils/request'

// 登录方法
export function login(username, password, code, uuid, zzdCode) {
  const data = {
    username,
    password,
    code,
    uuid,
    zzdCode
  }
  return request({
    url: '/login',
    headers: {
      isToken: false,
      repeatSubmit: false
    },
    method: 'post',
    data: data
  })
}

// 注册方法
export function register(data) {
  return request({
    url: '/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/getInfo',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}


// 获获取单点登录地址
export function getDddlUrl(query) {
  return request({
    url: 'dddl/getDddlUrl',
    method: "get",
    params: query,
  })
}

// 移动监测页面跳转
export function getYdjcymtz(query) {
  return request({
    url: '/dddl/ydjcymtz',
    method: "get",
    params: query,
  })
}