<template>
  <el-dialog
    title="基础设施告警"
    :visible.sync="showFlag"
    top="0"
    width="100%"
    @close="close"
  >
    <div>
      <el-select v-model="mkValue" placeholder="请选择" @change="lookWarning">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <div v-if="mkValue == '断点监测记录'">
        <el-table
          :data="breakpointMonitorData"
          border
          v-loading="breakpointLoading"
          element-loading-text="加载中..."
        >
          <el-table-column prop="monitorIp" label="监测IP" align="center" />
          <el-table-column prop="monitorTime" label="监测时间" align="center" />
          <el-table-column prop="ipStatus" label="IP状态" align="center">
            <template slot-scope="scope">
              <el-tag :type="getStatusType(scope.row.ipStatus)">
                {{ getStatusText(scope.row.ipStatus) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div style="margin-top: 20px; text-align: center">
          <el-pagination
            @current-change="handleBreakpointPageChange"
            :current-page="breakpointQueryParams.pageNum"
            :page-size="breakpointQueryParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, prev, pager, next, jumper"
            :total="breakpointTotal"
          />
        </div>
      </div>
      <div style="width: 100%; height: calc(100vh - 206px)" v-else>
        <iframe
          :src="src"
          frameborder="no"
          style="width: 100%; height: calc(100% - 58px)"
          scrolling="auto"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { getYdjcymtz } from "@/api/login";
import { listBreakpointMonitor } from "@/api/monitor/breakpointMonitor";
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    projectName: {
      type: String,
      default: "",
    },
    yyId: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      showFlag: false,
      src: "",
      mkValue: "",
      options: [
        {
          value: "",
          label: "基础设施监测",
        },
        {
          value: "应用性能监测",
          label: "应用性能监控",
        },
        {
          value: "用户访问监测",
          label: "用户访问监控",
        },
        {
          value: "断点监测记录",
          label: "断点监测记录",
        },
      ],
      // 断点监测弹窗相关数据
      applicationId: null,
      breakpointMonitorData: [],
      breakpointLoading: false,
      breakpointTotal: 0,
      breakpointQueryParams: {
        pageNum: 1,
        pageSize: 10,
        dataId: null, // 应用ID，用于查询该应用的断点监测数据
        type: 1,
      },
    };
  },
  watch: {
    show: function (val) {
      this.showFlag = val;
      if (val) {
        this.lookWarning();
      }
    },
  },
  computed: {},
  mounted() {},
  methods: {
    close() {
      this.showFlag = false;
      this.$emit("close");
    },
    lookWarning() {
      if (this.mkValue == "断点监测记录") {
        this.applicationId = this.yyId;
        this.breakpointMonitorData = [];
        this.breakpointTotal = 0;
        // 重置分页参数
        this.breakpointQueryParams.pageNum = 1;
        this.breakpointQueryParams.pageSize = 10;
        this.loadBreakpointMonitorData();
      } else {
        getYdjcymtz({ mk: this.mkValue, projectName: this.projectName }).then(
          (res) => {
            if (res.code == 200) {
              this.src = res.msg;
            }
          }
        );
      }
    },

    // 加载断点监测数据
    async loadBreakpointMonitorData() {
      try {
        this.breakpointLoading = true;
        // 设置应用ID查询参数
        this.breakpointQueryParams.dataId = this.applicationId;
        const response = await listBreakpointMonitor(
          this.breakpointQueryParams
        );

        if (response.code === 200 && response.data) {
          // 处理返回的数据
          this.breakpointMonitorData = (response.data.list || []).map(
            (item) => ({
              ...item,
              // 格式化监测时间
              monitorTime: item.cTime,
              // 格式化监测IP
              monitorIp: item.monitorIp || item.jcIp || item.ip,
              // 格式化IP状态
              ipStatus: item.ipStatus || item.status,
              // 响应时间
              responseTime: item.responseTime || item.xyTime,
              // 错误信息
              errorMsg: item.errorMsg || item.errorMessage || item.cwxx,
            })
          );

          this.breakpointTotal = response.data.total || 0;
        } else {
          this.breakpointMonitorData = [];
          this.breakpointTotal = 0;
          this.$message.error(response.msg || "获取断点监测数据失败");
        }
      } catch (error) {
        this.breakpointMonitorData = [];
        this.breakpointTotal = 0;
        this.$message.error("加载断点监测数据失败");
      } finally {
        this.breakpointLoading = false;
      }
    },

    // 断点监测分页 - 当前页变化
    handleBreakpointPageChange(page) {
      this.breakpointQueryParams.pageNum = page;
      this.loadBreakpointMonitorData();
    },
    // 获取状态类型（用于el-tag的type属性）
    getStatusType(status) {
      if (!status) return "info";
      const statusStr = status.toString().toLowerCase();
      if (statusStr === "1" || statusStr === "正常" || statusStr === "normal") {
        return "success";
      } else if (
        statusStr === "0" ||
        statusStr === "异常" ||
        statusStr === "error"
      ) {
        return "danger";
      }
      return "info";
    },

    // 获取状态文本
    getStatusText(status) {
      if (!status) return "未知";
      const statusStr = status.toString().toLowerCase();
      if (statusStr === "1" || statusStr === "正常" || statusStr === "normal") {
        return "正常";
      } else if (
        statusStr === "2" ||
        statusStr === "异常" ||
        statusStr === "error"
      ) {
        return "异常";
      }
      return status.toString();
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
