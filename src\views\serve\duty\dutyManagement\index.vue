<template>
  <div class="wrap">
    <div class="card">
      <div class="cardTitle">{{ !showRule ? "排班日历" : "排班规则" }}</div>
      <div class="flex-c" style="justify-content: flex-end">
        <div class="btn flex-c-c" v-if="!showRule" @click="showDialog()">
          新增排班
        </div>
        <div class="btn flex-c-c" v-if="!showRule" @click="showRule = true">
          排班规则
        </div>
        <div class="btn flex-c-c" v-if="showRule">新增规则</div>
        <div class="btn flex-c-c" v-if="showRule" @click="showRule = false">
          返回排班日历
        </div>
      </div>
      <schedule v-if="!showRule" @checkDetail="checkDetail"></schedule>
      <rulelist v-if="showRule"></rulelist>
    </div>
    <div class="card" v-if="!showRule">
      <div class="cardTitle">值班人员列表</div>
      <el-table :data="datalist">
        <el-table-column prop="name" label="姓名" align="center" />
        <el-table-column prop="dept" label="部门" align="center" />
        <el-table-column prop="lxfs" label="联系方式" align="center" />
        <el-table-column prop="pbzt" label="排班状态" align="center" />
      </el-table>
    </div>

    <el-dialog :visible.sync="show" :title="title" width="700px">
      <div style="display: flex; justify-content: center">
        <el-form
          :model="form"
          :rules="rules"
          label-width="fit-content"
          ref="form"
          label-position="top"
        >
          <el-form-item label="日期" prop="daterange">
            <el-date-picker
              v-model="form.daterange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="人员" prop="person">
            <el-select v-model="form.person" style="width: 100%">
              <el-option
                v-for="(item, i) in personList"
                :key="i"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="班次" prop="work">
            <el-select v-model="form.work" style="width: 100%">
              <el-option
                v-for="(item, i) in workList"
                :key="i"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
          <div class="flex-c">
            <el-form-item label="值班时间" prop="startTime">
              <el-time-select
                v-model="form.startTime"
                :picker-options="{
                  start: '05:30',
                  step: '00:15',
                  end: '18:30',
                }"
                placeholder="开始时间"
              >
              </el-time-select>
            </el-form-item>
            <span style="padding: 20px 20px 0 20px; box-sizing: border-box">
              至
            </span>
            <el-form-item label=" " prop="endTime">
              <el-time-select
                v-model="form.endTime"
                :picker-options="{
                  start: '05:30',
                  step: '00:15',
                  end: '18:30',
                }"
                placeholder="结束时间"
              >
              </el-time-select>
            </el-form-item>
          </div>
          <el-form-item label="排班规则" prop="rule">
            <el-select v-model="form.rule" style="width: 100%">
              <el-option
                v-for="(item, i) in ruleList"
                :key="i"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
          <div class="footer">
            <el-button type="primary" @click="submit"> 确 定 </el-button>
            <el-button @click="cancel"> 取 消 </el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import schedule from "@/views/serve/duty/dutyManagement/components/Schedule.vue";
import rulelist from "@/views/serve/duty/dutyManagement/components/RuleList.vue";
export default {
  components: { schedule, rulelist },
  data() {
    return {
      showRule: false,
      datalist: [
        { name: "萧铁", dept: "运维部", lxfs: "13988887963", pbzt: "值班中" },
        { name: "罗倩玉", dept: "运维部", lxfs: "13988887962", pbzt: "待值班" },
        { name: "易恒", dept: "运维部", lxfs: "13988887964", pbzt: "待值班" },
      ],
      //弹窗
      show: false,
      title: "",
      form: {
        daterange: "",
        person: "",
        work: "",
        time: "",
        startTime: "",
        endTime: "",
        rule: "",
      },
      rules: {
        daterange: [{ required: true, message: "请选择", trigger: "change" }],
        person: [{ required: true, message: "请选择", trigger: "change" }],
        work: [{ required: true, message: "请选择", trigger: "change" }],
        rule: [{ required: true, message: "请选择", trigger: "change" }],
        startTime: [{ required: true, message: "请选择", trigger: "change" }],
        endTime: [{ required: true, message: "请选择", trigger: "change" }],
      },
      personList: [{ label: "张三", value: "张三" }],
      workList: [{ label: "早班", value: "早班" }],
      ruleList: [{ label: "无", value: "无" }],
    };
  },
  methods: {
    checkDetail(value) {
      this.reset();
      this.title = "编辑排班";
      this.form = value;
      // console.log(this.form);
      this.show = true;
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
        }
      });
    },
    showDialog() {
      this.title = "新增排班";
      this.show = true;
      this.reset();
    },
    cancel() {
      this.show = false;
      this.reset();
    },
    reset() {
      this.form = {
        daterange: "",
        person: "",
        work: "",
        time: "",
        startTime: "",
        endTime: "",
        rule: "",
      };
      this.resetForm("form");
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-bottom: 10px;
    padding-left: 18px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 2px;
      left: 0;
      width: 10px;
      height: 20px;
      background: url("~@/assets/images/cardTitle_icon.png");
      background-size: 100% 100%;
    }
  }
}
.btn {
  padding: 3px 11px;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 5px 5px 5px 5px;
  border: 1px solid #e8ebf1;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #1d2129;
  line-height: 22px;
  margin-left: 12px;
  cursor: pointer;
}

::v-deep .el-form-item__label {
  padding: 0 !important;
}
::v-deep .el-form-item__label:before {
  content: "" !important;
}
.footer {
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
}
</style>
