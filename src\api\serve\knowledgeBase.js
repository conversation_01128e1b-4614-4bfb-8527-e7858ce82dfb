import request from "@/utils/request";

// 新的知识库接口------------------------
// 获取知识库树形结构
export function getLibraryTree(query) {
  return request({
    url: "/tyywpt/tTyywZskNew/getLibraryTree",
    method: "get",
    params: query,
  });
}

// 新增编辑知识库
export function editLibrary(data) {
  return request({
    url: "/tyywpt/tTyywZskNew/editLibrary",
    method: "post",
    data,
  });
}

// 删除知识库
export function deleteLibrary(id) {
  return request({
    url: "/tyywpt/tTyywZskNew/deleteLibrary/" + id,
    method: "delete",
  });
}

// 获取知识库详情
export function getLibraryDetail(id) {
  return request({
    url: "/tyywpt/tTyywZskNew/queryLibraryDetail/" + id,
    method: "get",
  });
}

// 分类管理相关接口
// 新增分类
export function addCategory(data) {
  return request({
    url: "/tyywpt/tTyywZskNew/addType",
    method: "post",
    data: data,
  });
}

// 编辑分类
export function updateCategory(data) {
  return request({
    url: "/tyywpt/tTyywZskNew/editType",
    method: "post",
    data: data,
  });
}

// 删除分类
export function deleteCategory(id) {
  return request({
    url: "/tyywpt/tTyywZskNew/deleteType/" + id,
    method: "delete",
  });
}

//项目知识库
// 获取项目知识库树形结构
export function getProjectLibraryTree(query) {
  return request({
    url: "/tyywpt/tTyywZskNew/getProjectLibraryTree",
    method: "get",
    params: query,
  });
}
