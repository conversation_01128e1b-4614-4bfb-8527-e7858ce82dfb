<template>
  <div class="container">
    <div class="flex-s">
      <div class="card" style="margin-right: 12px; flex: 1">
        <div class="cardTitle">人员总览</div>
        <div class="ryzl flex-c-c">
          <div class="flex-c">
            <img src="@/assets/images/property/ryzl.png" class="icon" />
            <div class="num">{{ ryzl }}<span class="unit">人</span></div>
          </div>
        </div>
      </div>
      <div class="card" style="margin-right: 12px; flex: 1">
        <div class="cardTitle">人员行业分布</div>
        <div id="barchart" style="width: 100%; height: 180px"></div>
      </div>
      <div class="card" style="flex: 1">
        <div class="cardTitle">人员标签分布</div>
        <div class="tagList flex-c">
          <div
            class="tag flex-c-c"
            v-for="(x, j) in rybqList"
            :key="j"
            :style="{
              border: 'solid 1px ' + colorList[j % 3],
              background: colorList[j % 3] + '10',
              color: colorList[j % 3],
            }"
          >
            {{ x.name }}{{ x.value }}
          </div>
        </div>
      </div>
    </div>

    <div class="card">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        label-width="100px"
        class="queryForm"
      >
        <el-form-item label="姓名" prop="xm">
          <el-input
            v-model="queryParams.xm"
            placeholder="请输入"
            clearable
            style="width: 160px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="所属单位" prop="ssdw">
          <el-select
            v-model="queryParams.ssdw"
            placeholder="请选择"
            clearable
            style="width: 160px"
          >
            <el-option
              v-for="(item, i) in ssdwOptions"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标签" prop="bq">
          <el-select
            v-model="queryParams.bq"
            placeholder="请选择"
            clearable
            style="width: 160px"
          >
            <el-option
              v-for="(item, i) in bqOptions"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin-left: 30px">
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">
            查询
          </el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="card" style="padding: 0 20px 20px 0">
      <div class="tabList flex-c">
        <div
          class="tab"
          v-for="(item, i) in tabList"
          :key="i"
          :class="tabIndex == i ? 'tab_active' : ''"
          @click="changeTab(i)"
        >
          {{ item }}
        </div>
      </div>
      <div class="btns flex-c">
        <el-checkbox
          v-model="checkAll"
          :indeterminate="checkSome"
          style="margin-right: 20px"
          @change="selectAll"
        >
          全选
        </el-checkbox>
        <el-button
          size="small"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          >新增</el-button
        >
        <el-button size="small" @click="handleDetele">删除</el-button>
        <el-button size="small" @click="handleImport">导入</el-button>
        <el-button size="small" @click="handleExport">导出</el-button>
      </div>
      <div class="list flex-c">
        <div class="item" v-for="(item, i) in datalist" :key="i">
          <div class="top flex-c">
            <img v-if="item.img" :src="item.img" class="img" />
            <img
              v-else
              src="@/assets/images/property/avatar_default.png"
              class="img"
            />
            <div class="top_right flex-s">
              <div class="top_info">
                <div class="flex-c">
                  <div class="name">{{ item.name }}</div>
                  <img
                    v-if="item.xb == '0'"
                    src="@/assets/images/property/male.png"
                    class="gender_icon"
                  />
                  <img
                    v-if="item.xb == '1'"
                    src="@/assets/images/property/female.png"
                    class="gender_icon"
                  />
                  <div v-if="item.tag" class="tag flex-c-c">{{ item.tag }}</div>
                </div>
                <div class="flex-c">
                  <img src="@/assets/images/property/phone.png" class="icon" />
                  <div class="value">{{ item.dh || "-" }}</div>
                </div>
                <div class="flex-c">
                  <img
                    src="@/assets/images/property/position.png"
                    class="icon"
                  />
                  <div class="value">{{ item.zw || "-" }}</div>
                </div>
                <div class="flex-c">
                  <img src="@/assets/images/property/dept.png" class="icon" />
                  <div class="value">{{ item.dept || "-" }}</div>
                </div>
              </div>
              <el-checkbox
                v-model="item.isSelected"
                @change="changeCheckbox(i)"
              />
            </div>
          </div>
          <div class="bottom flex-b">
            <el-button type="text" class="btn">档案</el-button>
            <el-divider direction="vertical"></el-divider>
            <el-button type="text" class="btn">编辑</el-button>
            <el-divider direction="vertical"></el-divider>
            <el-button type="text" class="btn">删除</el-button>
          </div>
        </div>
      </div>
      <div style="display: flex; justify-content: flex-end; margin-top: 20px">
        <el-pagination
          @current-change="getList"
          :current-page="queryParams.pageNum"
          :page-size="queryParams.pageNum"
          layout="total, prev, pager, next"
          :total="total"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  data() {
    return {
      ryzl: 817,
      barData: [
        { name: "政府", value: 40 },
        { name: "政府", value: 30 },
        { name: "政府", value: 20 },
        { name: "政府", value: 30 },
      ],
      rybqList: [
        { name: "安全联络人", value: 211 },
        { name: "联络安全官", value: 67 },
        { name: "首席安全运营官", value: 1 },
      ],
      colorList: ["#1677FF", "#16A34A", "#DC2626"],
      //
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        xm: "",
        ssdw: "",
        bq: "",
      },
      total: 50,
      ssdwOptions: [],
      bqOptions: [],
      //
      tabList: ["人员管理卡片", "人员管理列表"],
      tabIndex: 0,
      checkAll: false,
      checkSome: false,
      datalist: [
        {
          id: "1",
          name: "杨雨雅",
          xb: "0",
          tag: "首席安全运营官",
          img: "",
          dh: "18329017056",
          zw: "系统集成项目管理工程师",
          dept: "永康市大数据局",
          isSelected: false,
        },
        {
          id: "2",
          name: "杨雨雅",
          xb: "1",
          tag: "安全联络人",
          img: "",
          dh: "18329017056",
          zw: "系统集成项目管理工程师",
          dept: "永康市大数据局",
          isSelected: false,
        },
        // {
        //   id: "3",
        //   name: "杨雨雅",
        //   xb: "1",
        //   tag: "安全联络人",
        //   img: "",
        //   dh: "18329017056",
        //   zw: "系统集成项目管理工程师",
        //   dept: "永康市大数据局",
        //   isSelected: false,
        // },
        // {
        //   id: "4",
        //   name: "杨雨雅",
        //   xb: "1",
        //   tag: "",
        //   img: "",
        //   dh: "18329017056",
        //   zw: "系统集成项目管理工程师",
        //   dept: "永康市大数据局",
        //   isSelected: false,
        // },
        // {
        //   id: "5",
        //   name: "杨雨雅",
        //   xb: "0",
        //   tag: "",
        //   img: "",
        //   dh: "18329017056",
        //   zw: "系统集成项目管理工程师",
        //   dept: "永康市大数据局",
        //   isSelected: false,
        // },
        // {
        //   id: "6",
        //   name: "杨雨雅",
        //   xb: "0",
        //   tag: "",
        //   img: "",
        //   dh: "18329017056",
        //   zw: "系统集成项目管理工程师",
        //   dept: "永康市大数据局",
        //   isSelected: false,
        // },
        // {
        //   id: "7",
        //   name: "杨雨雅",
        //   xb: "0",
        //   tag: "",
        //   img: "",
        //   dh: "18329017056",
        //   zw: "系统集成项目管理工程师",
        //   dept: "永康市大数据局",
        //   isSelected: false,
        // },
        // {
        //   id: "8",
        //   name: "杨雨雅",
        //   xb: "0",
        //   tag: "",
        //   img: "",
        //   dh: "18329017056",
        //   zw: "系统集成项目管理工程师",
        //   dept: "永康市大数据局",
        //   isSelected: false,
        // },
      ],
    };
  },
  mounted() {
    this.getList();
    this.initBarChart(this.barData);
  },
  methods: {
    changeTab(i) {
      this.tabIndex = i;
    },
    selectAll(value) {
      if (!value) {
        this.datalist.forEach((item) => (item.isSelected = false));
        this.changeCheckbox();
      } else {
        this.datalist.forEach((item) => (item.isSelected = true));
        this.changeCheckbox();
      }
    },
    changeCheckbox() {
      this.checkSome = this.datalist.some((item) => item.isSelected == true);
      this.checkAll = this.datalist.every((item) => item.isSelected == true);
      if (this.checkAll) this.checkSome = false;
    },
    getList() {
      // console.log("Search keyword:", this.searchKeyword);
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        xm: "",
        ssdw: "",
        bq: "",
      };
    },
    handleAdd() {},
    handleDetele() {},
    handleImport() {},
    handleExport() {},
    initBarChart(data) {
      let chart = echarts.init(document.getElementById("barchart"));
      let option = {
        color: ["#3AA1FF"],
        tooltip: {
          trigger: "axis",
        },
        grid: {
          left: "2%",
          right: 0,
          bottom: 0,
          top: "16%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: data.map((x) => x.name),
          boundaryGap: true,
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#858D9D",
          },
          axisLine: {
            lineStyle: {
              color: "#DEE3E9",
            },
          },
        },
        yAxis: {
          type: "value",
          name: "单位：个",
          splitNumber: 4,
          axisLabel: {
            color: "#667085",
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: "#DEE3E9",
              type: "dotted",
            },
          },
        },
        series: [
          {
            name: "",
            type: "bar",
            barWidth: "12%",
            data: data.map((x) => x.value),
          },
        ],
      };
      chart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  padding-bottom: 30px;
  .card {
    width: 100%;
    border-radius: 15px;
    padding: 20px 20px;
    box-sizing: border-box;
    background-color: #fff;
    height: auto;
    margin-bottom: 12px;
    .cardTitle {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 700;
      font-size: 18px;
      color: #1d2129;
      line-height: 24px;
      text-align: left;
      margin-bottom: 15px;
    }
  }
}
.ryzl {
  width: 100%;
  height: 174px;
  border-radius: 10px;
  border: 1px solid #e5e6eb;
  .icon {
    width: 30px;
    height: 24px;
    margin-right: 12px;
  }
  .num {
    font-family: Roboto, Roboto;
    font-weight: 600;
    font-size: 30px;
    color: #1d2129;
    line-height: 36px;
    margin-right: 4px;
  }
  .unit {
    font-size: 16px;
  }
}
.tagList {
  flex-wrap: wrap;
  .tag {
    padding: 2px 8px;
    box-sizing: border-box;
    border-radius: 4px;
    font-family: Roboto, Roboto;
    font-weight: 400;
    font-size: 12px;
    color: #1677ff;
    margin-right: 12px;
  }
}
.queryForm {
  ::v-deep .el-form-item {
    margin-bottom: 0;
  }
}
.tabList {
  padding: 0 20px;
  box-sizing: border-box;
  .tab {
    margin-right: 56px;
    padding: 20px 0;
    box-sizing: border-box;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #1d2129;
    line-height: 22px;
    text-align: left;
    cursor: pointer;
  }
  .tab_active {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 700;
    font-size: 14px;
    color: #1d2129;
    line-height: 22px;
    border-bottom: solid 3px #0057fe;
  }
}
.btns {
  margin: 20px;
}
.list {
  flex-wrap: wrap;
  margin: 0 20px;
  box-sizing: border-box;
  .item {
    width: calc((100% - 12 * 3px) / 4);
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #e5e7eb;
    margin-right: 12px;
    margin-bottom: 12px;
    padding: 17px 17px 0 17px;
    box-sizing: border-box;
    .top {
      .img {
        width: 64px;
        height: 100px;
        margin-right: 16px;
      }
      .top_right {
        width: calc(100% - 64px - 16px);
        .top_info {
          width: 100%;
          .name {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 16px;
            color: #1f2937;
            line-height: 24px;
            text-align: left;
          }
          .gender_icon {
            width: 16px;
            height: 16px;
            margin: 0 8px 0 4px;
          }
          .tag {
            padding: 2px 8px;
            box-sizing: border-box;
            background: #eff6ff;
            border-radius: 4px 4px 4px 4px;
            font-family: Roboto, Roboto;
            font-weight: 400;
            font-size: 12px;
            color: #1d4ed8;
            line-height: 18px;
          }
          .icon {
            width: 14px;
            height: 14px;
            margin-right: 8px;
          }
          .value {
            font-family: Source Han Sans, Source Han Sans;
            font-weight: 400;
            font-size: 14px;
            color: #4e5969;
            line-height: 28px;
          }
        }
      }
    }
    .bottom {
      margin-top: 12px;
      border-top: solid 1px #e5e7eb;
      .btn {
        flex: 1;
        text-align: center;
      }
    }
    &:nth-child(4n + 4) {
      margin-right: 0;
    }
  }
}
.flex-s {
  display: flex;
  align-items: stretch;
}
</style>
