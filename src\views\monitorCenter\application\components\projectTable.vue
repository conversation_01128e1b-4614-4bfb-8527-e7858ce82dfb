<template>
  <div>
    <el-table :data="tableData" border>
      <el-table-column type="index" label="序号" align="center">
      </el-table-column>
      <el-table-column prop="filePath" label="文件类型" align="center">
        <template slot-scope="scope">
          <div class="file_type">
            <!-- 图片类型显示缩略图 -->
            <div v-if="getFileTypeByUrl(scope.row.filePath) === 4" class="image-preview">
              <img
                :src="scope.row.filePath"
                :alt="scope.row.fileName"
                class="thumbnail"
                @error="handleImageError"
              />
            </div>
            <!-- 其他类型显示图标 -->
            <div v-else>
              <img
                :src="getFileIcon(getFileTypeByUrl(scope.row.filePath))"
                :alt="scope.row.fileName"
              />
              <span class="icon-info">{{
                getExtensionFromUrl(scope.row.filePath)
              }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="fileName" label="文件名称" align="center">
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <!-- <el-button type="text" @click="handleUpLoad(scope.$index, scope.row)">上传</el-button> -->
          <el-button type="text" @click="handlePreview(scope.$index, scope.row)"
            >预览</el-button
          >
          <el-button
            type="text"
            @click="handleDownload(scope.$index, scope.row)"
            >下载</el-button
          >
          <el-button
            type="text"
            @click="handleDelete(scope.$index, scope.row)"
            v-if="showDelete"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  props: {
    tableData: {
      type: Array,
      default: () => {
        return [];
      },
    },
    showDelete: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  methods: {
    handleDelete(index, row) {
      this.$emit("handleDelete", index, row);
    },
    handlePreview(index, row) {
      window.open(row.filePath, "_blank");
    },
    handleDownload(index, row) {
      const a = document.createElement("a");
      a.href = row.filePath;
      a.target = "_blank";
      a.rel = "noopener noreferrer";
      a.style.display = "none";

      document.body.appendChild(a);
      a.click();

      document.body.removeChild(a);
    },
    handleImageError(event) {
      // 图片加载失败时显示默认图片图标
      event.target.src = this.getFileIcon(4);
      event.target.style.width = "32px";
      event.target.style.height = "32px";
    },
    getFileTypeByUrl(url) {
      const extension = this.getExtensionFromUrl(url);
      const typeMap = {
        doc: 1,
        docx: 1,
        xls: 2,
        xlsx: 2,
        xlsm: 2,
        xlsb: 2,
        pdf: 3,
        jpg: 4,
        jpeg: 4,
        png: 4,
        gif: 4,
        bmp: 4,
        webp: 4,
        svg: 4,
        ico: 4,
      };
      return typeMap[extension] || 0;
    },
    getExtensionFromUrl(url) {
      if (typeof url !== "string" || url.trim() === "") return "";
      const filenameMatch = url.match(/[^/?#]+$/);
      if (!filenameMatch) return "";
      const filename = filenameMatch[0];
      const lastDotIndex = filename.lastIndexOf(".");
      if (
        lastDotIndex === -1 ||
        lastDotIndex === 0 ||
        lastDotIndex === filename.length - 1
      ) {
        return "";
      }
      return filename.slice(lastDotIndex + 1).toLowerCase();
    },
    getFileIcon(type) {
      const iconMap = {
        1: require("@/assets/images/serve/word.png"), // 文档
        2: require("@/assets/images/serve/excel.png"), // 表格
        3: require("@/assets/images/serve/pdf.png"), // PDF
        4: "", // 图片类型不使用图标，直接显示缩略图
      };

      return iconMap[type] ;
    },
  },
};
</script>

<style lang="scss">
.icon-info {
  font-size: 22px;
  margin-left: 10px;
  width: 55px;
}

.file_type {
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-preview {
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnail {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
    transform: scale(1.05);
  }
}
</style>