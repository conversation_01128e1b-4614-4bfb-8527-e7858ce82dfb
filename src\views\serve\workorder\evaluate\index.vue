<template>
  <div>
    <div class="flex-s">
      <div class="card" style="flex: 1; margin-right: 12px">
        <div class="cardTitle">工单综合评价</div>
        <div class="flex-c" style="width: 100%">
          <div class="item" v-for="(item, i) in leftList" :key="i">
            <div class="name">{{ item.name }}</div>
            <div class="value">{{ item.value }}</div>
            <div class="unit">单位：{{ item.unit }}</div>
          </div>
        </div>
      </div>
      <div class="card" style="flex: 1">
        <div class="cardTitle">工单解决趋势</div>
        <div id="linechart" style="width: 100%; height: 180px"></div>
      </div>
    </div>
    <div class="card">
      <div class="cardTitle">详细数据</div>
      <el-table :data="datalist">
        <el-table-column prop="yf" label="月份" align="center" />
        <el-table-column prop="gdsl" label="工单数量" align="center" />
        <el-table-column prop="jjl" label="解决率" align="center" />
        <el-table-column prop="pjjjsc" label="平均解决时长" align="center" />
        <el-table-column prop="myd" label="满意度" align="center" />
      </el-table>
      <div style="display: flex; justify-content: flex-end; margin-top: 20px">
        <el-pagination
          @current-change="getList"
          :current-page="queryParams.pageNum"
          :page-size="queryParams.pageNum"
          layout="total, prev, pager, next"
          :total="total"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  components: {},
  data() {
    return {
      leftList: [
        { name: "平均解决时长", value: "0.4", unit: "小时" },
        { name: "问题解决率", value: "50", unit: "%" },
        { name: "用户满意度", value: "2.3/5", unit: "分" },
      ],
      lineData: [
        { name: "6月", value1: 20, value2: 3 },
        { name: "7月", value1: 30, value2: 3 },
        { name: "8月", value1: 15, value2: 3 },
        { name: "9月", value1: 35, value2: 6 },
        { name: "10月", value1: 10, value2: 4 },
        { name: "11月", value1: 12, value2: 3 },
      ],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 100,
      datalist: [
        {
          yf: "1月",
          gdsl: "32",
          jjl: "95%",
          pjjjsc: "6.0小时",
          myd: "4.6/5",
        },
        {
          yf: "2月",
          gdsl: "32",
          jjl: "95%",
          pjjjsc: "6.0小时",
          myd: "4.6/5",
        },
        {
          yf: "3月",
          gdsl: "32",
          jjl: "95%",
          pjjjsc: "6.0小时",
          myd: "4.6/5",
        },
      ],
    };
  },
  mounted() {
    this.initLineChart(this.lineData);
    this.getList();
  },
  methods: {
    getList() {},
    initLineChart(data) {
      let chart = echarts.init(document.getElementById("linechart"));
      let option = {
        color: ["#3AA1FF", "#36CB6D"],
        tooltip: {
          trigger: "axis",
        },
        grid: {
          left: "2%",
          right: "5%",
          bottom: 0,
          top: "20%",
          containLabel: true,
        },
        legend: {
          icon: "square",
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 20,
          right: "5%",
        },
        xAxis: {
          type: "category",
          data: data.map((x) => x.name),
          boundaryGap: true,
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#858D9D",
          },
          axisLine: {
            lineStyle: {
              color: "#DEE3E9",
            },
          },
        },
        yAxis: {
          type: "value",
          name: "单位：个",
          splitNumber: 4,
          axisLabel: {
            color: "#667085",
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: "#DEE3E9",
              type: "dotted",
            },
          },
        },
        series: [
          {
            name: "响应率",
            type: "line",
            showSymbol: false,
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#3AA1FF",
                },
                {
                  offset: 1,
                  color: " #3AA1FF20",
                },
              ]),
            },
            emphasis: {
              focus: "series",
            },
            data: data.map((x) => x.value1),
          },
          {
            name: "解决率",
            type: "line",
            showSymbol: false,
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#36CB6D",
                },
                {
                  offset: 1,
                  color: "#36CB6D20",
                },
              ]),
            },
            emphasis: {
              focus: "series",
            },
            data: data.map((x) => x.value2),
          },
        ],
      };
      chart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-bottom: 10px;
    padding-left: 18px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 2px;
      left: 0;
      width: 10px;
      height: 20px;
      background: url("~@/assets/images/cardTitle_icon.png");
      background-size: 100% 100%;
    }
  }
  .subtitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    padding-left: 10px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 6px;
      width: 3px;
      height: 13px;
      background-color: #0057fe;
      border-radius: 3px;
    }
  }
}

.item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  margin-right: 15px;
  padding: 22px 0;
  box-sizing: border-box;
  .name {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 28px;
    text-align: center;
    margin-bottom: 8px;
  }
  .value {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 28px;
    line-height: 42px;
    text-align: center;
    margin-bottom: 8px;
  }
  .unit {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 16px;
    color: #4e5969;
    line-height: 28px;
    text-align: center;
  }
  &:nth-child(1) {
    background: linear-gradient(180deg, #f6f7ff 0%, #ececff 100%);
    .value {
      color: #9900ff;
    }
  }
  &:nth-child(2) {
    background: linear-gradient(180deg, #f5fef2 0%, #e6feee 100%);
    .value {
      color: #36cb6d;
    }
  }
  &:nth-child(3) {
    background: linear-gradient(180deg, #f2f9fe 0%, #e6f4fe 100%);
    .value {
      color: #3ba1ff;
    }
  }
  &:last-child {
    margin-right: 0cm;
  }
}
.flex-s {
  display: flex;
  align-items: stretch;
}
</style>
