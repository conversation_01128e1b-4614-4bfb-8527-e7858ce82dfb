<template>
  <el-dialog title="编辑系统" :visible="showEdit" width="65%" @close="close">
    <el-form
      ref="myForm"
      :model="myForm"
      label-width="80px"
      label-position="top"
      inline
    >
      <div class="card">
        <div class="cardTitle">基本信息</div>
        <div class="itemList flex-c">
          <el-form-item label="系统名称" class="item_grid_3">
            <el-input
              v-model="myForm.xtmc"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="单位名称" class="item_grid_3">
            <el-input
              v-model="myForm.dwmc"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="是否是互联网系统" class="item_grid_3">
            <el-select
              v-model="myForm.sfshlwxt"
              clearable
              size="small"
              style="width: 100%"
            >
              <el-option
                v-for="(item, i) in boolOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="系统重要性" class="item_grid_3">
            <el-select
              v-model="myForm.xtzyx"
              clearable
              size="small"
              style="width: 100%"
            >
              <el-option
                v-for="(item, i) in zyxOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="系统状态" class="item_grid_3">
            <el-select
              v-model="myForm.xtzt"
              clearable
              size="small"
              style="width: 100%"
            >
              <el-option
                v-for="(item, i) in xtztOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="系统类型" class="item_grid_3">
            <el-select
              v-model="myForm.xtlx"
              clearable
              size="small"
              style="width: 100%"
            >
              <el-option
                v-for="(item, i) in xtlxOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="系统标签" class="item_grid_3">
            <el-select
              v-model="myForm.xtbq"
              clearable
              multiple
              size="small"
              style="width: 100%"
            >
              <el-option
                v-for="(item, i) in bqOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="上线时间" class="item_grid_3">
            <el-input
              v-model="myForm.sxsj"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="运维截止时间" class="item_grid_3">
            <el-input
              v-model="myForm.ywjzsj"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="运维人员" class="item_grid_3">
            <el-input
              v-model="myForm.ywry"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="系统编码" class="item_grid_3">
            <el-input
              v-model="myForm.xybm"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="card">
        <div class="flex-c">
          <div class="cardTitle">系统地址</div>
          <el-button
            icon="el-icon-plus"
            size="small"
            style="margin: 0 0 6px 20px"
            >新增WEB</el-button
          >
        </div>
        <el-table :data="myForm.xtdzList">
          <el-table-column prop="ym" label="域名" align="center" />
          <el-table-column prop="ip" label="IP" align="center" />
          <el-table-column prop="dk" label="端口" align="center" />
          <el-table-column prop="gxsj" label="更新时间" align="center" />
          <el-table-column label="操作" align="center">
            <template slot-scope="scope"></template>
          </el-table-column>
          <template slot="empty">
            <div class="tableEmpty">
              <img src="@/assets/images/property/empty.png" class="img" />
              <span>暂无数据</span>
            </div>
          </template>
        </el-table>
      </div>
      <div class="card">
        <div class="cardTitle">关联项目</div>
        <el-table :data="myForm.glxmList">
          <el-table-column prop="xmbm" label="项目编码" align="center" />
          <el-table-column prop="xmmc" label="项目名称" align="center" />
          <el-table-column prop="ssbm" label="所属部门" align="center" />
          <el-table-column prop="lxsj" label="立项时间" align="center" />
          <el-table-column label="操作" align="center">
            <template slot-scope="scope"></template>
          </el-table-column>
          <template slot="empty">
            <div class="tableEmpty">
              <img src="@/assets/images/property/empty.png" class="img" />
              <span>暂无数据</span>
            </div>
          </template>
        </el-table>
      </div>
      <div class="card">
        <div class="flex-c">
          <div class="cardTitle">关联IP资产</div>
          <el-button size="small" style="margin: 0 0 6px 20px"
            >选择关联资产</el-button
          >
        </div>
        <el-table :data="myForm.glipzcList">
          <el-table-column prop="zcmc" label="资产名称" align="center" />
          <el-table-column prop="zcip" label="资产IP" align="center" />
          <el-table-column prop="zczyx" label="资产重要性" align="center" />
          <el-table-column prop="zczt" label="资产状态" align="center" />
          <el-table-column prop="zcpj" label="资产评级" align="center" />
          <el-table-column label="操作" align="center">
            <template slot-scope="scope"></template>
          </el-table-column>
          <template slot="empty">
            <div class="tableEmpty">
              <img src="@/assets/images/property/empty.png" class="img" />
              <span>暂无数据</span>
            </div>
          </template>
        </el-table>
      </div>
      <div class="card">
        <div class="flex-c">
          <div class="cardTitle">联系人</div>
          <el-button
            icon="el-icon-plus"
            size="small"
            style="margin: 0 0 6px 20px"
          >
            新增其他联系人
          </el-button>
        </div>
        <div class="lineTitle">分管联系人</div>
        <div class="itemList flex-c">
          <el-form-item label="联系人" class="item_grid_3">
            <el-input
              v-model="myForm.fglxr['lxr']"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="联系方式" class="item_grid_3">
            <el-input
              v-model="myForm.fglxr['lxfs']"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="邮箱" class="item_grid_3">
            <el-input
              v-model="myForm.fglxr['yx']"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </div>
        <div class="lineTitle">部门负责人</div>
        <div class="itemList flex-c">
          <el-form-item label="联系人" class="item_grid_3">
            <el-input
              v-model="myForm.bmfzr['lxr']"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="联系方式" class="item_grid_3">
            <el-input
              v-model="myForm.bmfzr['lxfs']"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="邮箱" class="item_grid_3">
            <el-input
              v-model="myForm.bmfzr['yx']"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </div>
        <div class="lineTitle">第一联系人</div>
        <div class="itemList flex-c">
          <el-form-item label="联系人" class="item_grid_3">
            <el-input
              v-model="myForm.dylxr['lxr']"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="联系方式" class="item_grid_3">
            <el-input
              v-model="myForm.dylxr['lxfs']"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="邮箱" class="item_grid_3">
            <el-input
              v-model="myForm.dylxr['yx']"
              size="small"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </div>
      </div>
      <span class="footer">
        <el-button type="primary" @click="close" size="small">确 定</el-button>
        <el-button @click="close" size="small">取 消</el-button>
      </span>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  props: {
    showEdit: {
      type: Boolean,
      default: false,
    },
    form: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      myForm: {
        xtdzList: [],
        glxmList: [],
        glipzcList: [],
        fglxr: { lxr: "", lxfs: "", yx: "" },
        bmfzr: { lxr: "", lxfs: "", yx: "" },
        dylxr: { lxr: "", lxfs: "", yx: "" },
      },
      boolOptions: [
        { label: "是", value: "是" },
        { label: "否", value: "否" },
      ],
      zyxOptions: [],
      xtztOptions: [],
      xtlxOptions: [],
      bqOptions: [],
    };
  },
  mounted() {
    this.myForm = { ...this.myForm, ...this.form };
    console.log(this.myForm);
    // this.myForm = JSON.parse(JSON.stringify(this.form));
  },
  methods: {
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  padding: 20px 20px;
  padding-bottom: 0;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  // margin-bottom: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-bottom: 10px;
    padding-left: 18px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 2px;
      left: 0;
      width: 10px;
      height: 20px;
      background: url("~@/assets/images/cardTitle_icon.png");
      background-size: 100% 100%;
    }
  }
  .lineTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 500;
    font-size: 16px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin: 12px 0 0 32px;
  }
}

.itemList {
  flex-wrap: wrap;
  padding: 0 20px;
  box-sizing: border-box;
  .item_grid_3 {
    width: 30%;
    margin-left: 16px;
  }
}
.footer {
  margin-top: 20px;
  border-top: solid 1px #e5e6eb;
  padding: 20px 40px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.tableEmpty {
  padding: 10px 0 16px 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #1d2129;
  line-height: 22px;
  text-align: center;
  .img {
    width: 82px;
    height: 82px;
    margin-bottom: 6px;
  }
}
::v-deep .el-dialog {
  border-radius: 12px;
  overflow: hidden;
  .el-dialog__body {
    padding: 0;
    max-height: 640px;
    overflow-y: scroll;
    padding-bottom: 20px;
    .el-form-item__label {
      padding: 0;
      line-height: 30px;
    }
    .el-form-item {
      margin-bottom: 10px;
    }
  }
}
</style>
