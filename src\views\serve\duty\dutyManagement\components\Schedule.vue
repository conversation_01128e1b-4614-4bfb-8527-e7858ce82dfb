<template>
  <div class="wrap">
    <div class="tableTitle">
      <div class="flex-c" style="justify-content: center">
        <img src="@/assets/images/serve/arrow_left.png" @click="getLastMonth" />
        <div class="currentMonth">
          {{ showMonth }}
        </div>
        <img
          src="@/assets/images/serve/arrow_right.png"
          @click="getNextMonth"
        />
      </div>
    </div>
    <div class="weekList flex-c">
      <div class="weekItem" v-for="(item, i) in weekArr" :key="i">
        {{ item }}
      </div>
    </div>
    <div class="dayList" v-loading="loading">
      <div class="dayItem" v-for="(item, i) in monthArr" :key="i">
        <div v-if="i >= startDayCode - 1 && i <= endDayCode - 1">
          <div class="dayName">{{ new Date(item.date).getDate() }}</div>
          <div
            class="personList"
            v-if="item.personList && item.personList.length > 0"
          >
            <div
              class="personItem"
              v-for="(x, j) in item.personList"
              :key="j"
              :style="{
                background: new Date(item.date) >= new Date() ? x.color : '',
              }"
              @click="checkDetail(x)"
            >
              {{ x.person }} {{ x.time }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "schedule",
  data() {
    return {
      year: "",
      currentMonth: "",
      showMonth: "",
      loading: false,
      weekArr: ["日", "一", "二", "三", "四", "五", "六"],
      monthArr: [],
      startDayCode: -1,
      endDayCode: -1,
      startDate: "",
      endDate: "",
    };
  },
  watch: {
    showMonth: {
      handler() {
        let x = [this.startDate, this.endDate];
        this.$emit("changeMonth", x);
      },
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.date = new Date();
      let day = this.date;
      let year = day.getFullYear();
      let month = day.getMonth() + 1;

      this.getCurrentMonth(year, month);

      setTimeout(() => {
        this.monthArr[1].personList = [
          { person: "严彬", time: "07:00-15:00", color: "red" },
          { person: "严彬", time: "07:00-15:00", color: "lightblue" },
          { person: "严彬", time: "07:00-15:00", color: "orange" },
        ];
        this.monthArr[29].personList = [
          { person: "严彬", time: "07:00-15:00", color: "#EF5AA1" },
          { person: "严彬", time: "07:00-15:00", color: "#F59A23" },
          { person: "严彬", time: "07:00-15:00", color: "#02A7F0" },
        ];
        this.$forceUpdate();
      }, 500);
    },
    checkDetail(x){
      this.$emit('checkDetail',x)
    },

    //获取当前月份
    getCurrentMonth(yearParams, monthParams) {
      let date = new Date();
      if (yearParams) {
        date = new Date(yearParams, monthParams - 1, 1);
        this.year = yearParams;
      }

      if (date.getMonth() == 1 && date.getDate() == 29) {
        date = new Date(this.year, date.getMonth(), 28); //防止闰年
      } else {
        date = new Date(this.year, date.getMonth(), date.getDate());
      }
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      month = (month < 10 ? "0" : "") + month;
      this.currentMonth = year + "年" + month + "月";
      this.showMonth = this.currentMonth;

      this.getMonthArr(year, month);
    },
    //上一月
    getLastMonth() {
      let year = this.showMonth.slice(0, 4);
      let month = this.showMonth.slice(5, 7);
      if (month == 1) {
        //如果是0，则说明是1月份，上一个月就是去年的12月
        year = parseInt(year) - 1;
        month = 12;
      } else {
        month = parseInt(month) - 1;
      }
      month = month < 10 ? "0" + month : month;
      this.showMonth = year + "年" + month + "月";
      this.getMonthArr(year, month);
    },
    //下一月
    getNextMonth() {
      let year = this.showMonth.slice(0, 4);
      let month = this.showMonth.slice(5, 7);
      if (month == 12) {
        //如果是0，则说明是1月份，上一个月就是去年的12月
        year = parseInt(year) + 1;
        month = 1;
      } else {
        month = parseInt(month) + 1;
      }
      month = month < 10 ? "0" + month : month;
      this.showMonth = year + "年" + month + "月";
      this.getMonthArr(year, month);
    },
    //更改月份
    changeMonth(e) {
      if (e == null || e == "") {
        this.getCurrentMonth();
      } else {
        let year = e.slice(0, 4);
        let month = parseInt(e.slice(5, 7));
        this.getCurrentMonth(year, month);
        let x = [this.startDate, this.endDate];
        this.$emit("changeMonth", x);
      }
      this.chooseMonth = false;
    },
    //获取月份数组
    getMonthArr(year, month) {
      //获取第一天
      this.startDate = new Date(year, parseInt(month - 1), 1);
      let dayCode1 = this.startDate.getDay(); //判断月初是周几
      let startDate = new Date(this.startDate);
      startDate.setDate(startDate.getDate() - dayCode1); //月份第一天（不一定是1号）（周日起）

      //获取最后一天
      this.endDate = new Date(year, parseInt(month), 0); // 获取当月最后一天
      let dayCode2 = this.endDate.getDay(); // 判断月末是周几（0 表示周日）
      let endDate = new Date(this.endDate);
      endDate.setDate(endDate.getDate() + (6 - dayCode2)); // 月份最后一天（不一定是当月最后一天）（周六止）

      this.monthArr = [];
      let currentDate = new Date(startDate);

      while (currentDate <= endDate) {
        this.monthArr.push({
          date: new Date(currentDate),
        });
        currentDate.setDate(currentDate.getDate() + 1);
      }

      this.startDayCode = dayCode1 + 1; // 日历第一天
      this.endDayCode = this.monthArr.length - (6 - dayCode2); // 日历最后一天
      // console.log(this.monthArr, this.startDayCode, this.endDayCode);
    },
    //月份有多少天
    getMonthLength(year, month) {
      let d = new Date(year, parseInt(month) - 1, 1);
      // 将日期设置为下月一号
      d.setMonth(d.getMonth() + 1);
      d.setDate("1");
      // 获取本月最后一天
      d.setDate(d.getDate() - 1);
      return d.getDate();
    },
  },
};
</script>

<style scoped lang="scss">
.tableTitle {
  position: relative;
  .currentMonth {
    font-weight: 700;
    font-size: 16px;
    color: #222222;
    margin: 0 105px;
  }
}
.weekList {
  width: 100%;
  margin-top: 20px;
  .weekItem {
    flex: 1;
    height: 50px;
    background: #f1f5f8;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 500;
    font-size: 16px;
    color: #1d2129;
    border: solid 1px #dce2e880;
  }
}
.dayList {
  width: 100%;
  display: flex;
  justify-content: start;
  flex-wrap: wrap;
  .dayItem {
    width: calc((100%) / 7);
    min-height: 90px;
    font-weight: 500;
    font-size: 20px;
    color: #1d2129;
    border: solid 1px hsla(210, 21%, 89%, 0.502);
    position: relative;
    display: flex;
    justify-content: center;
    .dayName {
      position: absolute;
      top: 14px;
      left: 14px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      color: #1d2129;
    }
  }
  .day_blue {
    background-color: #ecf5ff;
    border-bottom: 2px solid #409eff;
  }
  .personList {
    width: 130px;
    background: #f2f4f7;
    box-shadow: 2px 2px 5px 0px rgba(0, 0, 0, 0.15);
    margin: 3px 0;
    .personItem {
      padding: 5px 6px;
      box-sizing: border-box;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #1d2129;
      line-height: 22px;
      text-align: left;
    }
  }
}

.flex-b {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flex-c {
  display: flex;
  align-items: center;
}
</style>
