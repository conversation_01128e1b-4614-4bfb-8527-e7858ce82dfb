<template>
  <div>
    <el-table :data="datalist">
      <template v-for="(item, i) in keyLabelList">
        <el-table-column
          :key="i"
          :prop="item.key"
          :label="item.label"
          align="center"
          v-if="
            item.key == 'ecsTotal' ||
            item.key == 'rdsTotal' ||
            item.key == 'slbTotal' ||
            item.key == 'redisTotal' ||
            item.key == 'ossTotal' ||
            item.key == 'polarDbmTotal' ||
            item.key == 'polarDboTotal'
          "
        >
          <template slot-scope="scope">
            <div
              v-if="scope.row[item.key]"
              class="text_link"
              @click="itemClick(item)"
            >
              {{ scope.row[item.key] }}
            </div>
            <div v-else>--</div>
          </template>
        </el-table-column>
        <el-table-column
          :key="i"
          :prop="item.key"
          :label="item.label"
          align="center"
          :min-width="item.width"
          v-else-if="
            item.key == 'vcpuUtil' ||
            item.key == 'memoryUtil' ||
            item.key == 'diskUtil'
          "
        >
          <template slot-scope="scope">
            <div :class="scope.row[item.key] >= 90 ? 'color_red' : ''">
              {{ scope.row[item.key]
              }}{{ scope.row[item.key] && item.showUnit ? item.unit : "" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :key="i"
          :prop="item.key"
          :label="item.label"
          align="center"
          :min-width="item.width"
          :show-overflow-tooltip="item.showTooltip"
          v-else
        >
          <template slot-scope="scope">
            {{ scope.row[item.key] }}
          </template>
        </el-table-column>
      </template>
      <el-table-column
        label="操作"
        align="center"
        width="180"
        v-if="showControl"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="handleDetail(scope.$index, scope.row)">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  props: {
    datalist: {
      type: Array,
      default: () => [],
    },
    keyLabelList: {
      type: Array,
      default: () => [],
    },
    showControl: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    handleDetail() {},
    itemClick(item) {
      this.$emit("itemClick", item);
    },
  },
};
</script>

<style lang="scss" scoped>
.color_red{
  color: red;
}
</style>
