<template>
    <div>
        <div v-for="item, index in demandList" :key="item.type">
            <div class="all_table_title">
                <span>{{ item.title }}</span>
                <file-upload @input="uploadChange($event, item.type, index)" btnName="上传文件" :isShowTip="false"
                    :ref="'uploadRefs' + index" />
            </div>
            <my-table :tableData="item.tableData">
            </my-table>
        </div>

    </div>
</template>

<script>
import MyTable from "./projectTable.vue"
import { getFileList, addFileList } from "@/api/property/projectRequirements.js";
import FileUpload from "@/components/FileUpload/index.vue";

export default {
    components: {
        MyTable,
        FileUpload,
    },
    data() {
        return {
            demandList: [
                { title: '立项期间确认需求', type: 'APPROVAL', tableData: [] },
                { title: '建设期间变更需求', type: 'BUILD', tableData: [] },
                { title: '运维期间新增需求', type: 'OPERATION', tableData: [] },
            ],

        }
    },
    methods: {
        getFileListFn() {
            let yyId = this.$route.params.id || this.$route.query.id;
            getFileList({ yyId }).then(res => {
                if (res.code === 200) {
                    const responseData = res.data || {};
                    Object.keys(responseData).forEach(typeKey => {
                        const targetItem = this.demandList.find(item => item.type === typeKey);

                        if (targetItem) {
                            targetItem.tableData = responseData[typeKey] || [];
                        } 
                    });
                    this.demandList.forEach(item => {
                        if (!responseData[item.type]) {
                            item.tableData = [];
                        }
                    });
                }
            })
        },
        uploadChange(val, type, i) {
            let query = {
                filePath: val || '',
                fileName: val ? val.match(/\/([^\/]+)$/)[1] : '',
                type,
                yyId: this.$route.params.id || this.$route.query.id,
            }
            this.$refs['uploadRefs' + i][0].fileList = []
            addFileList(query).then(res => {
                if (res.code === 200) {
                    this.$modal.msgSuccess("新增成功");
                    this.getFileListFn()
                }
            })
        }
    },
    mounted() {
        this.getFileListFn()
    },
}

</script>

<style lang="scss" scoped>
.all_table_title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
    margin: 15px 0 10px;
}
</style>