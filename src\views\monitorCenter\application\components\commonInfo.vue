<template>
  <div class="wrap flex-c">
    <div class="line" v-for="(item, i) in dataList" :key="i">
      <div class="label">{{ item.name }}</div>
      <!-- 单位负责人：姓名脱敏处理 -->
      <div class="value-with-control" v-if="item.name == '单位负责人'">
        <span class="value">{{ formatName(item.value, i) }}</span>
        <i
          v-if="nameVisibilityMap[i]"
          class="el-icon-view control-icon"
          @click="toggleNameVisibility(i)"
          title="隐藏姓名"
        ></i>
        <i
          v-else
          class="custom-eye-hide control-icon"
          @click="toggleNameVisibility(i)"
          title="显示姓名"
        ></i>
      </div>
      <!-- 联系电话：手机号脱敏处理 -->
      <div class="value-with-control" v-else-if="item.name == '联系电话'">
        <span class="value">{{ formatPhone(item.value, i) }}</span>
        <i
          v-if="phoneVisibilityMap[i]"
          class="el-icon-view control-icon"
          @click="togglePhoneVisibility(i)"
          title="隐藏手机号"
        ></i>
        <i
          v-else
          class="custom-eye-hide control-icon"
          @click="togglePhoneVisibility(i)"
          title="显示手机号"
        ></i>
      </div>
      <!-- 联系电话：姓名脱敏处理 -->
      <div class="value-with-control" v-else-if="item.name == '开发厂商联系人'">
        <span class="value">{{ formatName(item.value, i) }}</span>
        <i
          v-if="nameVisibilityMap[i]"
          class="el-icon-view control-icon"
          @click="toggleNameVisibility(i)"
          title="隐藏姓名"
        ></i>
        <i
          v-else
          class="custom-eye-hide control-icon"
          @click="toggleNameVisibility(i)"
          title="显示姓名"
        ></i>
      </div>
      <!-- 联系电话：手机号脱敏处理 -->
      <div
        class="value-with-control"
        v-else-if="item.name == '开发厂商联系电话'"
      >
        <span class="value">{{ formatPhone(item.value, i) }}</span>
        <i
          v-if="phoneVisibilityMap[i]"
          class="el-icon-view control-icon"
          @click="togglePhoneVisibility(i)"
          title="隐藏手机号"
        ></i>
        <i
          v-else
          class="custom-eye-hide control-icon"
          @click="togglePhoneVisibility(i)"
          title="显示手机号"
        ></i>
      </div>
      <!-- 联系电话：姓名脱敏处理 -->
      <div class="value-with-control" v-else-if="item.name == '运维厂商联系人'">
        <span class="value">{{ formatName(item.value, i) }}</span>
        <i
          v-if="nameVisibilityMap[i]"
          class="el-icon-view control-icon"
          @click="toggleNameVisibility(i)"
          title="隐藏姓名"
        ></i>
        <i
          v-else
          class="custom-eye-hide control-icon"
          @click="toggleNameVisibility(i)"
          title="显示姓名"
        ></i>
      </div>
      <!-- 联系电话：手机号脱敏处理 -->
      <div
        class="value-with-control"
        v-else-if="item.name == '运维厂商联系电话'"
      >
        <span class="value">{{ formatPhone(item.value, i) }}</span>
        <i
          v-if="phoneVisibilityMap[i]"
          class="el-icon-view control-icon"
          @click="togglePhoneVisibility(i)"
          title="隐藏手机号"
        ></i>
        <i
          v-else
          class="custom-eye-hide control-icon"
          @click="togglePhoneVisibility(i)"
          title="显示手机号"
        ></i>
      </div>
      <!-- 联系电话：姓名脱敏处理 -->
      <div class="value-with-control" v-else-if="item.name == '安全厂商联系人'">
        <span class="value">{{ formatName(item.value, i) }}</span>
        <i
          v-if="nameVisibilityMap[i]"
          class="el-icon-view control-icon"
          @click="toggleNameVisibility(i)"
          title="隐藏姓名"
        ></i>
        <i
          v-else
          class="custom-eye-hide control-icon"
          @click="toggleNameVisibility(i)"
          title="显示姓名"
        ></i>
      </div>
      <!-- 联系电话：手机号脱敏处理 -->
      <div
        class="value-with-control"
        v-else-if="item.name == '安全厂商联系电话'"
      >
        <span class="value">{{ formatPhone(item.value, i) }}</span>
        <i
          v-if="phoneVisibilityMap[i]"
          class="el-icon-view control-icon"
          @click="togglePhoneVisibility(i)"
          title="隐藏手机号"
        ></i>
        <i
          v-else
          class="custom-eye-hide control-icon"
          @click="togglePhoneVisibility(i)"
          title="显示手机号"
        ></i>
      </div>
      <!-- 其他字段：正常显示 -->
      <div class="value" v-else>{{ item.value || "-" }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    dataList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      // 姓名显示状态控制
      nameVisibilityMap: {},
      // 手机号显示状态控制
      phoneVisibilityMap: {},
    };
  },
  watch: {
    dataList: {
      handler() {
        this.initVisibility();
      },
      immediate: true,
    },
  },
  methods: {
    // 初始化显示状态
    initVisibility() {
      const nameVisibilityMap = {};
      const phoneVisibilityMap = {};
      this.dataList.forEach((item, index) => {
        nameVisibilityMap[index] = false; // 默认隐藏姓名
        phoneVisibilityMap[index] = false; // 默认隐藏手机号
      });
      this.nameVisibilityMap = nameVisibilityMap;
      this.phoneVisibilityMap = phoneVisibilityMap;
    },
    // 格式化姓名显示（脱敏处理）
    formatName(name, index) {
      if (!name || name === "-") return name;

      // 根据姓名显示状态决定是否脱敏
      if (this.nameVisibilityMap[index]) {
        return name; // 显示完整姓名
      } else {
        // 姓名脱敏处理
        if (name.length === 1) {
          return name; // 单字姓名不脱敏
        } else if (name.length === 2) {
          return name.charAt(0) + "*"; // 两字姓名：张*
        } else if (name.length === 3) {
          return name.charAt(0) + "*" + name.charAt(2); // 三字姓名：张*三
        } else {
          // 四字及以上姓名：保留首尾，中间用*代替
          return (
            name.charAt(0) +
            "*".repeat(name.length - 2) +
            name.charAt(name.length - 1)
          );
        }
      }
    },
    // 格式化手机号码显示
    formatPhone(phone, index) {
      if (!phone || phone === "-") return phone;

      // 检查是否为有效的手机号码格式
      if (!/^1[3-9]\d{9}$/.test(phone)) {
        return phone; // 如果不是标准手机号格式，直接返回原值
      }

      // 根据显示状态决定是否隐藏中间四位
      if (this.phoneVisibilityMap[index]) {
        return phone; // 显示完整手机号
      } else {
        return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2"); // 隐藏中间四位
      }
    },
    // 切换姓名显示状态
    toggleNameVisibility(index) {
      this.$set(this.nameVisibilityMap, index, !this.nameVisibilityMap[index]);
    },
    // 切换手机号码显示状态
    togglePhoneVisibility(index) {
      this.$set(
        this.phoneVisibilityMap,
        index,
        !this.phoneVisibilityMap[index]
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.wrap {
  flex-wrap: wrap;
}

.line {
  display: flex;
  align-items: center;
  width: 34%;
  margin: 6px 20px 16px 20px;

  .label {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #4e5969;
    line-height: 20px;
    text-align: left;
    width: 180px;
    margin-right: 16px;
    white-space: nowrap;
  }

  .value {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #1d2129;
    line-height: 20px;
    text-align: left;
    width: 100%;
  }

  .value-with-control {
    display: flex;
    align-items: center;
    width: 100%;

    .value {
      flex: 1;
      margin-right: 8px;
    }

    .control-icon {
      cursor: pointer;
      color: #409eff;
      font-size: 14px;
      margin-left: 4px;

      &:hover {
        color: #66b1ff;
      }
    }
  }
}
</style>
