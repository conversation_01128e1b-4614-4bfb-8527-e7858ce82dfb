<template>
  <div style="width: 100%; height: 230px" id="dbjb"></div>
</template>

<script>
import * as echarts from "echarts";
export default {
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  mounted() {},
  watch: {
    data: {
      handler(val) {
        this.initChart(val);
      },
      deep: true,
    },
  },
  methods: {
    initChart(data) {
      let chart = echarts.init(document.getElementById("dbjb"));
      let option = {
        tooltip: {
          trigger: "axis",
        },
        grid: {
          left: "2%",
          right: 0,
          bottom: 0,
          top: "16%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: data.map((x) => x.name),
          boundaryGap: true,
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#858D9D",
          },
          axisLine: {
            lineStyle: {
              color: "#DEE3E9",
            },
          },
        },
        yAxis: {
          type: "value",
          name: "单位：",
          splitNumber: 4,
          axisLabel: {
            color: "#667085",
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: "#DEE3E9",
              type: "dotted",
            },
          },
        },
        series: [
          {
            name: "",
            type: "bar",
            barWidth: "15%",
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#0057FE88",
                },
                {
                  offset: 1,
                  color: "#0057FE00",
                },
              ]),
            },
            data: data.map((x) => x.num),
          },
        ],
      };
      chart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
