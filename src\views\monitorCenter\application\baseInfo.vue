<template>
  <div class="wrap flex-c">

    <div class="line">
      <div class="label">应用编码</div>
      <div class="value">{{ data.yybm }}</div>
    </div>
    <div class="line">
      <div class="label">应用管理员</div>
      <div class="value">{{ data.yygly }}</div>
    </div>
    <div class="line">
      <div class="label">应用类型</div>
      <div class="value">{{ data.yylx }}</div>
    </div>
    <div class="line">
      <div class="label">上线时间</div>
      <div class="value">{{ data.sxsj }}</div>
    </div>
    <div class="line">
      <div class="label">运维截止时间</div>
      <div class="value">{{ data.ywjzsj }}</div>
    </div>
    <div class="line">
      <div class="label">运维人员</div>
      <div class="value">{{ data.ywry }}</div>
    </div>
    <div class="line">
      <div class="label">系统状态</div>
      <div class="value">{{ data.xtzt }}</div>
    </div>
    <div class="line">
      <div class="label">建设依据</div>
      <div class="value">{{ data.jsyj }}</div>
    </div>
    <div class="line">
      <div class="label">建设层级</div>
      <div class="value">{{ data.jscj }}</div>
    </div>
    <div class="line">
      <div class="label">统建范围</div>
      <div class="value">{{ data.tjfw }}</div>
    </div>
    <div class="line">
      <div class="label">用户范围</div>
      <div class="value">{{ data.systemName }}</div>
    </div>
    <div class="line">
      <div class="label">发布端</div>
      <div class="value">{{ data.yhfw }}</div>
    </div>
    <div class="line">
      <div class="label">网络环境</div>
      <div class="value">{{ data.wlhj }}</div>
    </div>
    <div class="line">
      <div class="label">政务服务</div>
      <div class="value">{{ data.zwfw }}</div>
    </div>
    <div class="line">
      <div class="label">所属系统</div>
      <div class="value">{{ data.ssxt }}</div>
    </div>
    <div class="line">
      <div class="label">履职领域</div>
      <div class="value">{{ data.lzly }}</div>
    </div>
    <div class="line">
      <div class="label">二级领域</div>
      <div class="value">{{ data.ejly }}</div>
    </div>
    <div class="line">
      <div class="label">应用领域</div>
      <div class="value">{{ data.yyly }}</div>
    </div>
    <div class="line">
      <div class="label">是否协同</div>
      <div class="value">{{ data.sfxt }}</div>
    </div>
    <div class="line">
      <div class="label">多跨场景</div>
      <div class="value">{{ data.dkcj }}</div>
    </div>
    <div class="line">
      <div class="label">体系贯通</div>
      <div class="value">{{ data.txgt }}</div>
    </div>
    <div class="line">
      <div class="label">属地</div>
      <div class="value">{{ data.sd }}</div>
    </div>









<!--    <div class="line">-->
<!--      <div class="label">系统名称</div>-->
<!--      <div class="value">{{ data.systemName }}</div>-->
<!--    </div>-->
<!--    <div class="line">-->
<!--      <div class="label">单位名称</div>-->
<!--      <div class="value">{{ data.dwmc }}</div>-->
<!--    </div>-->
<!--    <div class="line">-->
<!--      <div class="label">上线时间</div>-->
<!--      <div class="value">{{ data.sxsj }}</div>-->
<!--    </div>-->
<!--    <div class="line">-->
<!--      <div class="label">运维截止时间</div>-->
<!--      <div class="value">{{ data.ywjzsj }}</div>-->
<!--    </div>-->
<!--    <div class="line">-->
<!--      <div class="label">使用时间</div>-->
<!--      <div class="value">{{ data.sysj }}</div>-->
<!--    </div>-->
<!--    <div class="line">-->
<!--      <div class="label">机房信息</div>-->
<!--      <div class="value">{{ data.jfxx }}</div>-->
<!--    </div>-->
<!--    <div class="line">-->
<!--      <div class="label">运维人员</div>-->
<!--      <div class="value">{{ data.ywry }}</div>-->
<!--    </div>-->
<!--    <div class="line">-->
<!--      <div class="label">系统编码</div>-->
<!--      <div class="value">{{ data.xtbm }}</div>-->
<!--    </div>-->
<!--    <div class="line">-->
<!--      <div class="label">系统重要性</div>-->
<!--      <div class="value">{{ data.xtzyx }}</div>-->
<!--    </div>-->
<!--    <div class="line">-->
<!--      <div class="label">是否互联网系统</div>-->
<!--      <div class="value">{{ data.sfhlwxt }}</div>-->
<!--    </div>-->
<!--    <div class="line">-->
<!--      <div class="label">系统状态</div>-->
<!--      <div class="value">{{ data.xtzt }}</div>-->
<!--    </div>-->
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style lang="scss" scoped>
.wrap {
  flex-wrap: wrap;
}
.line {
  display: flex;
  align-items: center;
  width: 34%;
  margin: 6px 20px 16px 20px;
  .label {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #4e5969;
    line-height: 20px;
    text-align: left;
    width: 120px;
    margin-right: 16px;
    white-space: nowrap;
  }
  .value {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #1d2129;
    line-height: 20px;
    text-align: left;
    width: 100%;
  }
}
</style>
