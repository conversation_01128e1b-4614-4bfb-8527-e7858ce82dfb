<template>
  <div class="tabWrap">
    <div class="flex-c">
      <i class="el-icon-arrow-left icon"></i>
      <div class="tabName">{{ tablist[tabIndex].name }}</div>
    </div>
    <div class="tablist flex-c">
      <div
        class="tab"
        v-for="(item, i) in tablist"
        :key="i"
        :class="tabIndex == i ? 'tab_active' : ''"
        @click="changeTab(i, item)"
      >
        <span>{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    tabIndex: {
      type: Number,
      default: 0,
    },
    tablist: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      // tablist: [
      //   { name: "值班看板" },
      //   { name: "排班管理" },
      //   { name: "交接日志" },
      //   { name: "统计分析" },
      // ],
    };
  },
  methods: {
    changeTab(i, item) {
      item.index = i;
      this.$emit("changeTab", item);
    },
  },
};
</script>

<style lang="scss" scoped>
.tabWrap {
  width: 100%;
  height: 107px;
  background-image: url("~@/assets/images/childrenTabBg.png");
  background-size: 100% 100%;
  padding: 20px 0 0 16px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .icon {
    color: #fff;
    font-weight: 700;
  }
  .tabName {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #ffffff;
    line-height: 24px;
    text-align: left;
    margin-left: 8px;
  }
}
.tablist {
  margin-left: 40px;
  .tab {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-size: 14px;
    color: #ffffff;
    line-height: 22px;
    text-align: left;
    cursor: pointer;
    margin-right: 50px;
    padding-bottom: 10px;
  }
  .tab_active {
    position: relative;
    font-weight: 700;
    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      width: 56px;
      height: 2px;
      background-color: #fff;
    }
  }
}
</style>
