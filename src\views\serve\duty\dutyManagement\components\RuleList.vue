<template>
  <div class="container">
    <el-input
      placeholder="请输入关键字"
      v-model="queryParams.keyword"
      class="search-input"
      clearable
      size="small"
    >
      <el-button
        slot="append"
        icon="el-icon-search"
        @click="getList"
      ></el-button>
    </el-input>

    <el-table :data="tableData">
      <el-table-column
        prop="ruleNumber"
        label="规则编号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="ruleName"
        label="规则名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="scheduleCycle"
        label="排班周期"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="shift"
        label="班次"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="dutyTime"
        label="值班时段"
        align="center"
      ></el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handleEdit(scope.$index, scope.row)"
            >编辑</el-button
          >
          <el-button type="text" @click="handleDelete(scope.$index, scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <div style="display: flex; justify-content: flex-end; margin-top: 20px">
      <el-pagination
        @current-change="getList"
        :current-page="queryParams.pageNum"
        :page-size="queryParams.pageNum"
        layout="total, prev, pager, next"
        :total="total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      queryParams: {
        pageSize: 10,
        pageNum: 1,
        keyword: "",
      },
      total: 1,
      tableData: [
        {
          ruleNumber: "01",
          ruleName: "规则一",
          scheduleCycle: "周一、周二、周五",
          shift: "早班",
          dutyTime: "07:00-15:00",
        },
      ],
    };
  },
  methods: {
    getList() {
      // 处理搜索逻辑
      console.log("Search keyword:", this.searchKeyword);
    },
    handleEdit(index, row) {
      // 处理编辑逻辑
      console.log("Edit:", index, row);
    },
    handleDelete(index, row) {
      // 处理删除逻辑
      console.log("Delete:", index, row);
    },
  },
  created() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.search-input {
  margin-bottom: 20px;
  width: 300px;
}
</style>
