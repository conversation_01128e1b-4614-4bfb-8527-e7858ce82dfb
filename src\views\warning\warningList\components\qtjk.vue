<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-07-10 08:57:31
 * @LastEditors: wjb
 * @LastEditTime: 2025-07-17 08:57:52
-->
<template>
  <div>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item prop="incidentId">
        <el-input
          v-model="queryParams.incidentId"
          placeholder="告警ID"
          clearable
          @input="handledebounce"
          @clear="handleQuery"
        ></el-input>
      </el-form-item>
      <!-- <el-form-item prop="level">
        <el-select
          v-model="queryParams.level"
          placeholder="告警等级"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in levelOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="告警状态"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item prop="incidentNameText">
        <el-input
          v-model="queryParams.incidentNameText"
          placeholder="事件名称"
          clearable
          @input="handledebounce"
          @clear="handleQuery"
        ></el-input>
      </el-form-item>
      <el-form-item prop="czfs">
        <el-select
          v-model="queryParams.czfs"
          placeholder="处置方式"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in czfsOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <el-table :data="datalist">
      <el-table-column
        prop="incidentId"
        label="告警id"
        align="center"
        min-width="110"
      />
      <el-table-column
        prop="deptName"
        label="关联部门"
        align="center"
        min-width="80"
        show-overflow-tooltip
      />
      <el-table-column
        prop="yyName"
        label="关联应用"
        align="center"
        min-width="110"
      />
      <el-table-column
        prop=""
        label="告警等级"
        align="center"
        min-width="80"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div class="flex-c-c">
            <div
              class="tag flex-c-c"
              :class="
                scope.row.level == 'CRITICAL'
                  ? 'tag_red'
                  : scope.row.level == 'MAJOR'
                  ? 'tag_org'
                  : scope.row.level == 'MODERATE'
                  ? 'tag_yel'
                  : 'tag_blu'
              "
            >
              {{
                scope.row.level == "CRITICAL"
                  ? "特别紧急"
                  : scope.row.level == "MAJOR"
                  ? "紧急"
                  : scope.row.level == "MODERATE"
                  ? "重要"
                  : "一般"
              }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="createdTime"
        label="告警时间"
        align="center"
        min-width="110"
        show-overflow-tooltip
      />
      <el-table-column
        prop=""
        label="告警状态"
        align="center"
        min-width="80"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div class="flex-c-c">
            <div
              class="tag flex-c-c"
              :class="
                scope.row.processStatus == 'CLOSED'
                  ? 'tag_blu'
                  : scope.row.processStatus == 'PENDING'
                  ? 'tag_red'
                  : ''
              "
            >
              {{ scope.row.processStatus == "CLOSED" ? "已处理" : "处理中" }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="targetName"
        label="告警对象"
        align="center"
        min-width="110"
      />
      <!-- <el-table-column prop="assignees" label="处置人" align="center" /> -->
      <el-table-column
        prop="alertSourceNames"
        label="告警源名称"
        align="center"
        min-width="110"
      />
      <el-table-column
        prop="checks"
        label="检查项"
        align="center"
        min-width="110"
      />
      <el-table-column
        prop="incidentNameText"
        label="事件名称"
        align="center"
        min-width="140"
        show-overflow-tooltip
      />
      <el-table-column
        prop=""
        label="处置方式"
        align="center"
        min-width="80"
        ><template slot-scope="scope">
          <div v-if="scope.row.czfs == 1">忽略</div>
          <div v-if="scope.row.czfs == 2">转工单</div>
        </template></el-table-column
      >
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <el-button type="text" @click="goDetail(scope.row)">查看</el-button>
          <el-button
            type="text"
            v-if="!scope.row.czfs && !scope.row.gdId"
            @click="goHide(scope.row)"
            >忽略</el-button
          >
          <el-button type="text" v-else disabled>忽略</el-button>
          <el-button
            type="text"
            @click="showDialog(scope.row)"
            v-if="!scope.row.gdId"
            >转工单</el-button
          >
          <el-button type="text" disabled v-else>转工单</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="display: flex; justify-content: flex-end; margin-top: 20px">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        layout="total, prev, pager, next"
        :total="total"
      ></el-pagination>
    </div>

    <!-- 详情弹窗 -->
    <infoDialog :show="show" :info="info" @close="show = false"></infoDialog>

    <!-- 转工单弹窗 -->
    <addOrderDialog
      :show="show1"
      :info="info1"
      :deptName1="info1DeptName"
      :deptId1="info1DeptId"
      @close="show1 = false"
      @addSuccess="addSuccess"
    ></addOrderDialog>
  </div>
</template>

<script>
import { listYdgjList, hideGjHl } from "@/api/warning/warningList";
import addOrderDialog from "./addOrderDialog.vue";
import infoDialog from "./qtjkDialog.vue";
import { debounce } from "@/utils";

export default {
  components: { addOrderDialog, infoDialog },
  props: {
    yyId: {
      type: Number,
      default: null,
    },
    deptId: {
      type: Number,
      default: null,
    },
    dateArr: {
      type: Array,
      default: [],
    },
    level: {
      type: String,
      default: "",
    },
    activeName: {
      type: String,
      default: "",
    },
    gjStatus: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      datalist: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: "",
        incidentId: undefined,
      },
      // 防抖搜索函数
      debouncedSearch: null,
      statusOptions: [
        { label: "处理中", value: "UNRESOLVED" },
        { label: "已处理", value: "RESOLVED" },
      ],
      levelOptions: [
        { label: "特别紧急", value: "特别紧急" },
        { label: "紧急", value: "紧急" },
        { label: "重要", value: "重要" },
        { label: "一般", value: "一般" },
      ],
      czfsOptions: [
        { label: "忽略", value: 1 },
        { label: "转工单", value: 2 },
      ],
      //查看
      show: false,
      info: {},
      //新增工单
      show1: false,
      info1: {},
      info1DeptName: "",
      info1DeptId: null,
    };
  },
  watch: {
    yyId(newVal, oldVal) {
      this.getList();
    },
    deptId(newVal, oldVal) {
      this.getList();
    },
    dateArr(newVal, oldVal) {
      this.getList();
    },
    activeName(newVal, oldVal) {
      this.getList();
    },
    level(newVal, oldVal) {
      this.queryParams.level =
        newVal == 1
          ? "特别紧急"
          : newVal == 2
          ? "紧急"
          : newVal == 3
          ? "重要"
          : newVal == 4
          ? "一般"
          : "";
      this.getList();
    },
    gjStatus(newVal, oldVal) {
      this.getList();
    },
  },
  mounted() {
    // this.getList();
    // 初始化防抖搜索函数
    this.debouncedSearch = debounce(() => {
      this.handleQuery();
    }, 500); // 500ms 防抖延迟
  },
  methods: {
    //新增工单开始--
    showDialog(row) {
      this.show1 = true;
      this.info1 = {
        gdType: "3",
        title: row.yyName + "监测异常",
        describe:
          row.yyName + "于" + row.createdTime + "监测到应用异常，请及时处理。",
        priority: 3,
        handlerId: "",
        yyId: row.yyId,
        dataId: row.sjId,
        dataType: 6,
        csList: [],
      };
      this.info1DeptName = row.deptName;
      this.info1DeptId = row.deptId;
    },
    addSuccess() {
      this.show1 = false;
      this.getList();
    },
    //新增工单结束--
    // 防抖搜索处理
    handledebounce() {
      // 调用已初始化的防抖函数
      this.debouncedSearch();
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 分页 - 当前页变化
    handleCurrentChange(page) {
      this.queryParams.pageNum = page;
      this.getList();
    },
    // 获取应用列表
    async getList() {
      try {
        this.datalist = [];
        var params = {
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
          // status: this.queryParams.status,
          incidentId: this.queryParams.incidentId,
          level: this.queryParams.level,
          incidentNameText: this.queryParams.incidentNameText,
          czfs: this.queryParams.czfs,
          yyId: this.yyId,
          constructionUnitId: this.deptId,
          gjStatus: this.gjStatus,
        };
        if (this.dateArr && this.dateArr.length > 0) {
          params.startTime = this.dateArr[0];
          params.endTime = this.dateArr[1];
        } else {
          params.startTime = null;
          params.endTime = null;
        }
        const response = await listYdgjList(params);
        if (response.code === 200 && response.data) {
          this.datalist = response.data.list;
          this.total = response.data.total || 0;
        } else {
          this.datalist = [];
          this.total = 0;
          this.$message.error(response.msg || "获取应用监测失败");
        }
      } catch (error) {
        this.datalist = [];
        this.total = 0;
        this.$message.error("获取应用监测失败");
      }
    },
    goDetail(row) {
      this.info = row;
      this.show = true;
    },
    //忽略
    goHide(row) {
      hideGjHl({ id: row.sjId, type: 6 }).then((res) => {
        if (res.code == 200) {
          this.$message.success("忽略成功");
          this.getList();
        } else {
          this.$message.error(res.msg || "忽略失败");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.tag {
  width: fit-content;
  padding: 4px 12px;
  box-sizing: border-box;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  border-radius: 20px;
}
.tag_blu {
  background: #3ba1ff1a;
  color: #3ba1ff;
}
.tag_red {
  background: #ff00001a;
  color: #ff0000;
}
.tag_yel {
  background: #ffca3a1a;
  color: #ffca3a;
}
.tag_org {
  background: #ff7d001a;
  color: #ff7d00;
}
</style>
