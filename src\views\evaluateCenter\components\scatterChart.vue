<template>
  <div class="chart-wrapper">
    <div class="chart-title">{{ title }}</div>
    <div class="scatterChart" :id="'scatterChart' + time"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  name: 'pieChart',
  props: {
    title: {
      type: String,
      required: true,
    },
    color: {
      type: String,
    },
    chartData: {
      type: Array,
      required: true,
    }
  },
  data() {
    return {
      time: (Math.random() * 10000000).toFixed(0),
      chart: null,
      option: {},
      maxValue: 0,
      minValue: 100,
    }
  },
  mounted() {
    let that = this
    setTimeout(() => {
      that.initChart()
    })
  },
  methods: {
    initChart() {
      this.chartData.map(item => {
        if(item[0] > this.maxValue) {
          this.maxValue = item[0];
        }
        if(item[0] < this.minValue) {
          this.minValue = item[0];
        }
      })
      let chartId = 'scatterChart' + this.time
      this.chart = echarts.init(document.getElementById(chartId))
      this.option = {
        tooltip: {
          trigger: 'axis',
          formatter: function (obj) {
            var value = obj[0].value;
            // prettier-ignore
            return value[0].toFixed(2) + '<br>'
             + '次数：' + value[1]
          }
        },
        legend: {
          data: ['次数'],
          //align: 'left',
          right: 10,
          textStyle: {
            color: "#667085",
            fontSize: 14
          },
          itemWidth: 14,
          itemHeight: 10,
          itemGap: 13,
        },
        grid: {
          left: '8%',
          right: '4%',
          bottom: '14%',
          top: '18%',
        },
        xAxis: {
          min: this.minValue,
          max: this.maxValue,
          axisTick: {
            show: false,
          },
          axisLabel: {//y轴文字的配置
            show: true,
            textStyle: {
              color: "#667085",
              fontSize: 14
            }
          },
          axisLine: {
            lineStyle: {
              color: '#DEE3E9'
            }
          },
          splitLine: {
            show: false,
          }
        },
        yAxis: {
          type: 'value',
          name:"单位：次",//y轴上方的单位
          splitLine: {//分割线配置
            show: true,
            lineStyle: {
              color: "rgba(222,227,233,0.3)",
            }
          },
          axisLabel: {//y轴文字的配置
            show: true,
            textStyle: {
              color: "#667085",
              fontSize: 14
            }
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          }
        },
        series: [
          {
            color: this.color,
            name: '次数',
            symbolSize: 8,
            data: this.chartData,
            type: 'scatter'
          }
        ]
      }
      this.chart.setOption(this.option)
    }
  }
}
</script>

<style scoped lang="scss">
.chart-title {
  font-weight: 400;
  font-size: 18px;
  color: #1D2129;
  position: relative;
  padding-left: 10px;
}
.chart-title:before {
  content: '';
  display: block;
  border-radius: 3px;
  width: 3px;
  height: 13px;
  background: #0057FE;
  left: 0px;
  top: 6px;
  position: absolute;
}
.scatterChart {
  width: 550px;
  height: 200px;
  margin-top: 10px;
}
</style>
