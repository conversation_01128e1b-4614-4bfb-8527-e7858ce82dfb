import request from '@/utils/request'

// 查询供应商(厂商)列表
export function listSupplier(query) {
  return request({
    url: '/tyywpt/tTyywGys/list',
    method: 'get',
    params: query
  })
}

// 查询供应商(厂商)详细
export function getSupplier(id) {
  return request({
    url: '/tyywpt/tTyywGys/getInfo',
    method: 'get',
    params: { id: id }
  })
}

// 新增供应商(厂商)
export function addSupplier(data) {
  return request({
    url: '/tyywpt/tTyywGys/add',
    method: 'post',
    data: data
  })
}

// 修改供应商(厂商)
export function updateSupplier(data) {
  return request({
    url: '/tyywpt/tTyywGys/edit',
    method: 'put',
    data: data
  })
}

// 删除供应商(厂商)
export function delSupplier(ids) {
  return request({
    url: '/tyywpt/tTyywGys/remove',
    method: 'delete',
    params: { ids: ids }
  })
}

// 导出供应商(厂商)列表
export function exportSupplier(query) {
  return request({
    url: '/tyywpt/tTyywGys/export',
    method: 'post',
    params: query
  })
}
