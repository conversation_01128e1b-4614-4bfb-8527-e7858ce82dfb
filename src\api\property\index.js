import request from "@/utils/request";

// 资产总览
export function getZczl(query) {
  return request({
    url: "/zczl/zczl",
    method: "get",
    params: query,
  });
}

// 系统等保级别统计
export function getXtdbjbtj(query) {
  return request({
    url: "/zczl/xtdbjbtj",
    method: "get",
    params: query,
  });
}

// 应用所属单位分布
export function getYyssdwfb(query) {
  return request({
    url: "/zczl/yyssdwfb",
    method: "get",
    params: query,
  });
}

// 资产数量变化趋势
export function getZcslbhqs(query) {
  return request({
    url: "/zczl/zcslbhqs",
    method: "get",
    params: query,
  });
}

// 云资源使用量top5
export function getYzysylTop5(query) {
  return request({
    url: "/zczl/yzysylTop5",
    method: "get",
    params: query,
  });
}