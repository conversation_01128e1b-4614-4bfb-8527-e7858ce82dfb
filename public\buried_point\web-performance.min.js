var MITO_PERF=function(e){"use strict";var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(e,t,n){return e(n={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&n.path)}},n.exports),n.exports}var r,o,i=function(e){return e&&e.Math==Math&&e},a=i("object"==typeof globalThis&&globalThis)||i("object"==typeof window&&window)||i("object"==typeof self&&self)||i("object"==typeof t&&t)||function(){return this}()||Function("return this")(),u=function(e){try{return!!e()}catch(e){return!0}},c=!u((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f=Function.prototype.call,s=f.bind?f.bind(f):function(){return f.apply(f,arguments)},p={}.propertyIsEnumerable,l=Object.getOwnPropertyDescriptor,d={f:l&&!p.call({1:2},1)?function(e){var t=l(this,e);return!!t&&t.enumerable}:p},v=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},m=Function.prototype,h=m.bind,y=m.call,g=h&&h.bind(y),w=h?function(e){return e&&g(y,e)}:function(e){return e&&function(){return y.apply(e,arguments)}},b=w({}.toString),E=w("".slice),S=a.Object,C=w("".split),x=u((function(){return!S("z").propertyIsEnumerable(0)}))?function(e){return"String"==function(e){return E(b(e),8,-1)}(e)?C(e,""):S(e)}:S,P=a.TypeError,O=function(e){if(null==e)throw P("Can't call method on "+e);return e},T=function(e){return x(O(e))},M=function(e){return"function"==typeof e},L=function(e){return"object"==typeof e?null!==e:M(e)},j=function(e){return M(e)?e:void 0},_=function(e,t){return arguments.length<2?j(a[e]):a[e]&&a[e][t]},A=w({}.isPrototypeOf),R=_("navigator","userAgent")||"",I=a.process,F=a.Deno,k=I&&I.versions||F&&F.version,D=k&&k.v8;D&&(o=(r=D.split("."))[0]>0&&r[0]<4?1:+(r[0]+r[1])),!o&&R&&(!(r=R.match(/Edge\/(\d+)/))||r[1]>=74)&&(r=R.match(/Chrome\/(\d+)/))&&(o=+r[1]);var N=o,q=!!Object.getOwnPropertySymbols&&!u((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&N&&N<41})),H=q&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,B=a.Object,z=H?function(e){return"symbol"==typeof e}:function(e){var t=_("Symbol");return M(t)&&A(t.prototype,B(e))},W=a.String,$=a.TypeError,U=function(e){if(M(e))return e;throw $(function(e){try{return W(e)}catch(e){return"Object"}}(e)+" is not a function")},G=a.TypeError,J=Object.defineProperty,V=function(e,t){try{J(a,e,{value:t,configurable:!0,writable:!0})}catch(n){a[e]=t}return t},X=a["__core-js_shared__"]||V("__core-js_shared__",{}),Q=n((function(e){(e.exports=function(e,t){return X[e]||(X[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.19.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),K=a.Object,Y=w({}.hasOwnProperty),Z=Object.hasOwn||function(e,t){return Y(K(O(e)),t)},ee=0,te=Math.random(),ne=w(1..toString),re=function(e){return"Symbol("+(void 0===e?"":e)+")_"+ne(++ee+te,36)},oe=Q("wks"),ie=a.Symbol,ae=ie&&ie.for,ue=H?ie:ie&&ie.withoutSetter||re,ce=function(e){if(!Z(oe,e)||!q&&"string"!=typeof oe[e]){var t="Symbol."+e;q&&Z(ie,e)?oe[e]=ie[e]:oe[e]=H&&ae?ae(t):ue(t)}return oe[e]},fe=a.TypeError,se=ce("toPrimitive"),pe=function(e,t){if(!L(e)||z(e))return e;var n,r,o=null==(n=e[se])?void 0:U(n);if(o){if(void 0===t&&(t="default"),r=s(o,e,t),!L(r)||z(r))return r;throw fe("Can't convert object to primitive value")}return void 0===t&&(t="number"),function(e,t){var n,r;if("string"===t&&M(n=e.toString)&&!L(r=s(n,e)))return r;if(M(n=e.valueOf)&&!L(r=s(n,e)))return r;if("string"!==t&&M(n=e.toString)&&!L(r=s(n,e)))return r;throw G("Can't convert object to primitive value")}(e,t)},le=function(e){var t=pe(e,"string");return z(t)?t:t+""},de=a.document,ve=L(de)&&L(de.createElement),me=function(e){return ve?de.createElement(e):{}},he=!c&&!u((function(){return 7!=Object.defineProperty(me("div"),"a",{get:function(){return 7}}).a})),ye=Object.getOwnPropertyDescriptor,ge={f:c?ye:function(e,t){if(e=T(e),t=le(t),he)try{return ye(e,t)}catch(e){}if(Z(e,t))return v(!s(d.f,e,t),e[t])}},we=a.String,be=a.TypeError,Ee=function(e){if(L(e))return e;throw be(we(e)+" is not an object")},Se=a.TypeError,Ce=Object.defineProperty,xe={f:c?Ce:function(e,t,n){if(Ee(e),t=le(t),Ee(n),he)try{return Ce(e,t,n)}catch(e){}if("get"in n||"set"in n)throw Se("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},Pe=c?function(e,t,n){return xe.f(e,t,v(1,n))}:function(e,t,n){return e[t]=n,e},Oe=w(Function.toString);M(X.inspectSource)||(X.inspectSource=function(e){return Oe(e)});var Te,Me,Le,je=X.inspectSource,_e=a.WeakMap,Ae=M(_e)&&/native code/.test(je(_e)),Re=Q("keys"),Ie=function(e){return Re[e]||(Re[e]=re(e))},Fe={},ke=a.TypeError,De=a.WeakMap;if(Ae||X.state){var Ne=X.state||(X.state=new De),qe=w(Ne.get),He=w(Ne.has),Be=w(Ne.set);Te=function(e,t){if(He(Ne,e))throw new ke("Object already initialized");return t.facade=e,Be(Ne,e,t),t},Me=function(e){return qe(Ne,e)||{}},Le=function(e){return He(Ne,e)}}else{var ze=Ie("state");Fe[ze]=!0,Te=function(e,t){if(Z(e,ze))throw new ke("Object already initialized");return t.facade=e,Pe(e,ze,t),t},Me=function(e){return Z(e,ze)?e[ze]:{}},Le=function(e){return Z(e,ze)}}var We,$e={set:Te,get:Me,has:Le,enforce:function(e){return Le(e)?Me(e):Te(e,{})},getterFor:function(e){return function(t){var n;if(!L(t)||(n=Me(t)).type!==e)throw ke("Incompatible receiver, "+e+" required");return n}}},Ue=Function.prototype,Ge=c&&Object.getOwnPropertyDescriptor,Je=Z(Ue,"name"),Ve={EXISTS:Je,PROPER:Je&&"something"===function(){}.name,CONFIGURABLE:Je&&(!c||c&&Ge(Ue,"name").configurable)},Xe=n((function(e){var t=Ve.CONFIGURABLE,n=$e.get,r=$e.enforce,o=String(String).split("String");(e.exports=function(e,n,i,u){var c,f=!!u&&!!u.unsafe,s=!!u&&!!u.enumerable,p=!!u&&!!u.noTargetGet,l=u&&void 0!==u.name?u.name:n;M(i)&&("Symbol("===String(l).slice(0,7)&&(l="["+String(l).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!Z(i,"name")||t&&i.name!==l)&&Pe(i,"name",l),(c=r(i)).source||(c.source=o.join("string"==typeof l?l:""))),e!==a?(f?!p&&e[n]&&(s=!0):delete e[n],s?e[n]=i:Pe(e,n,i)):s?e[n]=i:V(n,i)})(Function.prototype,"toString",(function(){return M(this)&&n(this).source||je(this)}))})),Qe=Math.ceil,Ke=Math.floor,Ye=function(e){var t=+e;return t!=t||0===t?0:(t>0?Ke:Qe)(t)},Ze=Math.max,et=Math.min,tt=Math.min,nt=function(e){return(t=e.length)>0?tt(Ye(t),9007199254740991):0;var t},rt=function(e){return function(t,n,r){var o,i=T(t),a=nt(i),u=function(e,t){var n=Ye(e);return n<0?Ze(n+t,0):et(n,t)}(r,a);if(e&&n!=n){for(;a>u;)if((o=i[u++])!=o)return!0}else for(;a>u;u++)if((e||u in i)&&i[u]===n)return e||u||0;return!e&&-1}},ot={includes:rt(!0),indexOf:rt(!1)},it=ot.indexOf,at=w([].push),ut=function(e,t){var n,r=T(e),o=0,i=[];for(n in r)!Z(Fe,n)&&Z(r,n)&&at(i,n);for(;t.length>o;)Z(r,n=t[o++])&&(~it(i,n)||at(i,n));return i},ct=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],ft=ct.concat("length","prototype"),st={f:Object.getOwnPropertyNames||function(e){return ut(e,ft)}},pt={f:Object.getOwnPropertySymbols},lt=w([].concat),dt=_("Reflect","ownKeys")||function(e){var t=st.f(Ee(e)),n=pt.f;return n?lt(t,n(e)):t},vt=function(e,t){for(var n=dt(t),r=xe.f,o=ge.f,i=0;i<n.length;i++){var a=n[i];Z(e,a)||r(e,a,o(t,a))}},mt=/#|\.prototype\./,ht=function(e,t){var n=gt[yt(e)];return n==bt||n!=wt&&(M(t)?u(t):!!t)},yt=ht.normalize=function(e){return String(e).replace(mt,".").toLowerCase()},gt=ht.data={},wt=ht.NATIVE="N",bt=ht.POLYFILL="P",Et=ht,St=ge.f,Ct=function(e,t){var n,r,o,i,u,c=e.target,f=e.global,s=e.stat;if(n=f?a:s?a[c]||V(c,{}):(a[c]||{}).prototype)for(r in t){if(i=t[r],o=e.noTargetGet?(u=St(n,r))&&u.value:n[r],!Et(f?r:c+(s?".":"#")+r,e.forced)&&void 0!==o){if(typeof i==typeof o)continue;vt(i,o)}(e.sham||o&&o.sham)&&Pe(i,"sham",!0),Xe(n,r,i,e)}},xt=Object.keys||function(e){return ut(e,ct)},Pt=c?Object.defineProperties:function(e,t){Ee(e);for(var n,r=T(t),o=xt(t),i=o.length,a=0;i>a;)xe.f(e,n=o[a++],r[n]);return e},Ot=_("document","documentElement"),Tt=Ie("IE_PROTO"),Mt=function(){},Lt=function(e){return"<script>"+e+"<\/script>"},jt=function(e){e.write(Lt("")),e.close();var t=e.parentWindow.Object;return e=null,t},_t=function(){try{We=new ActiveXObject("htmlfile")}catch(e){}var e,t;_t="undefined"!=typeof document?document.domain&&We?jt(We):((t=me("iframe")).style.display="none",Ot.appendChild(t),t.src=String("javascript:"),(e=t.contentWindow.document).open(),e.write(Lt("document.F=Object")),e.close(),e.F):jt(We);for(var n=ct.length;n--;)delete _t.prototype[ct[n]];return _t()};Fe[Tt]=!0;var At=Object.create||function(e,t){var n;return null!==e?(Mt.prototype=Ee(e),n=new Mt,Mt.prototype=null,n[Tt]=e):n=_t(),void 0===t?n:Pt(n,t)},Rt=ce("unscopables"),It=Array.prototype;null==It[Rt]&&xe.f(It,Rt,{configurable:!0,value:At(null)});var Ft,kt=ot.includes;Ct({target:"Array",proto:!0},{includes:function(e){return kt(this,e,arguments.length>1?arguments[1]:void 0)}}),Ft="includes",It[Rt][Ft]=!0;var Dt;Dt="includes",w(a["Array"].prototype[Dt]);var Nt=w(d.f),qt=w([].push),Ht=function(e){return function(t){for(var n,r=T(t),o=xt(r),i=o.length,a=0,u=[];i>a;)n=o[a++],c&&!Nt(r,n)||qt(u,e?[n,r[n]]:r[n]);return u}},Bt={entries:Ht(!0),values:Ht(!1)}.values;Ct({target:"Object",stat:!0},{values:function(e){return Bt(e)}}),a.Object.values;function zt(e,t){void 0===t&&(t={});for(var n=function(e){for(var t=[],n=0;n<e.length;){var r=e[n];if("*"!==r&&"+"!==r&&"?"!==r)if("\\"!==r)if("{"!==r)if("}"!==r)if(":"!==r)if("("!==r)t.push({type:"CHAR",index:n,value:e[n++]});else{var o=1,i="";if("?"===e[u=n+1])throw new TypeError('Pattern cannot start with "?" at '+u);for(;u<e.length;)if("\\"!==e[u]){if(")"===e[u]){if(0===--o){u++;break}}else if("("===e[u]&&(o++,"?"!==e[u+1]))throw new TypeError("Capturing groups are not allowed at "+u);i+=e[u++]}else i+=e[u++]+e[u++];if(o)throw new TypeError("Unbalanced pattern at "+n);if(!i)throw new TypeError("Missing pattern at "+n);t.push({type:"PATTERN",index:n,value:i}),n=u}else{for(var a="",u=n+1;u<e.length;){var c=e.charCodeAt(u);if(!(c>=48&&c<=57||c>=65&&c<=90||c>=97&&c<=122||95===c))break;a+=e[u++]}if(!a)throw new TypeError("Missing parameter name at "+n);t.push({type:"NAME",index:n,value:a}),n=u}else t.push({type:"CLOSE",index:n,value:e[n++]});else t.push({type:"OPEN",index:n,value:e[n++]});else t.push({type:"ESCAPED_CHAR",index:n++,value:e[n++]});else t.push({type:"MODIFIER",index:n,value:e[n++]})}return t.push({type:"END",index:n,value:""}),t}(e),r=t.prefixes,o=void 0===r?"./":r,i="[^"+Wt(t.delimiter||"/#?")+"]+?",a=[],u=0,c=0,f="",s=function(e){if(c<n.length&&n[c].type===e)return n[c++].value},p=function(e){var t=s(e);if(void 0!==t)return t;var r=n[c],o=r.type,i=r.index;throw new TypeError("Unexpected "+o+" at "+i+", expected "+e)},l=function(){for(var e,t="";e=s("CHAR")||s("ESCAPED_CHAR");)t+=e;return t};c<n.length;){var d=s("CHAR"),v=s("NAME"),m=s("PATTERN");if(v||m){var h=d||"";-1===o.indexOf(h)&&(f+=h,h=""),f&&(a.push(f),f=""),a.push({name:v||u++,prefix:h,suffix:"",pattern:m||i,modifier:s("MODIFIER")||""})}else{var y=d||s("ESCAPED_CHAR");if(y)f+=y;else if(f&&(a.push(f),f=""),s("OPEN")){h=l();var g=s("NAME")||"",w=s("PATTERN")||"",b=l();p("CLOSE"),a.push({name:g||(w?u++:""),pattern:g&&!w?i:w,prefix:h,suffix:b,modifier:s("MODIFIER")||""})}else p("END")}}return a}function Wt(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function $t(e){return e&&e.sensitive?"":"i"}function Ut(e,t,n){return function(e,t,n){void 0===n&&(n={});for(var r=n.strict,o=void 0!==r&&r,i=n.start,a=void 0===i||i,u=n.end,c=void 0===u||u,f=n.encode,s=void 0===f?function(e){return e}:f,p="["+Wt(n.endsWith||"")+"]|$",l="["+Wt(n.delimiter||"/#?")+"]",d=a?"^":"",v=0,m=e;v<m.length;v++){var h=m[v];if("string"==typeof h)d+=Wt(s(h));else{var y=Wt(s(h.prefix)),g=Wt(s(h.suffix));if(h.pattern)if(t&&t.push(h),y||g)if("+"===h.modifier||"*"===h.modifier){var w="*"===h.modifier?"?":"";d+="(?:"+y+"((?:"+h.pattern+")(?:"+g+y+"(?:"+h.pattern+"))*)"+g+")"+w}else d+="(?:"+y+"("+h.pattern+")"+g+")"+h.modifier;else d+="("+h.pattern+")"+h.modifier;else d+="(?:"+y+g+")"+h.modifier}}if(c)o||(d+=l+"?"),d+=n.endsWith?"(?="+p+")":"$";else{var b=e[e.length-1],E="string"==typeof b?l.indexOf(b[b.length-1])>-1:void 0===b;o||(d+="(?:"+l+"(?="+p+"))?"),E||(d+="(?="+l+"|"+p+")")}return new RegExp(d,$t(n))}(zt(e,n),t,n)}function Gt(e,t,n){return e instanceof RegExp?function(e,t){if(!t)return e;for(var n=/\((?:\?<(.*?)>)?(?!\?)/g,r=0,o=n.exec(e.source);o;)t.push({name:o[1]||r++,prefix:"",suffix:"",modifier:"",pattern:""}),o=n.exec(e.source);return e}(e,t):Array.isArray(e)?function(e,t,n){var r=e.map((function(e){return Gt(e,t,n).source}));return new RegExp("(?:"+r.join("|")+")",$t(n))}(e,t,n):Ut(e,t,n)}var Jt,Vt=function(e,t){void 0===t&&(t=4);try{return parseFloat(e.toFixed(t))}catch(t){return e}},Xt=function(e){return"number"!=typeof e?null:Vt(e/Math.pow(1024,2))},Qt=function(e){window.addEventListener("beforeunload",e)},Kt=function(e){window.addEventListener("unload",e)},Yt=function(e,t){if(!e||0===e.length)return!1;if(!t||0===t.length)return!1;if(e.length>t.length)return!1;for(var n=0;n<e.length;n++)if(!(null==t?void 0:t.includes(e[n])))return!1;return!0},Zt=function(e){var t;return e?null===(t=e.match(/(?:http(?:s|):\/\/[^\/\s]+|)([^#?]+).*/))||void 0===t?void 0:t[1]:""},en=function(e,t){for(var n=e.map((function(e){return Gt(e)})),r=0;r<n.length;r++)if(n[r].exec(t))return!0;return!1},tn=function(e,t){var n=function(r){"pagehide"!==r.type&&"hidden"!==document.visibilityState||(e(r),t&&(removeEventListener("visibilitychange",n,!0),removeEventListener("pagehide",n,!0)))};addEventListener("visibilitychange",n,!0),addEventListener("pagehide",n,!0)},nn=function(){function e(){this.state=new Map}return e.prototype.set=function(e,t){this.state.set(e,t)},e.prototype.get=function(e){return this.state.get(e)},e.prototype.has=function(e){return this.state.has(e)},e.prototype.clear=function(){this.state.clear()},e.prototype.getValues=function(){return Array.from(this.state).reduce((function(e,t){var n=t[0],r=t[1];return e[n]=r,e}),{})},e}(),rn=function(){return!!window.performance&&!!window.performance.getEntriesByType&&!!window.performance.mark},on=function(){return!!window.PerformanceObserver},an=function(){return!!window.navigator},un=function(e){rn()?performance.mark(e):console.error("browser do not support performance")},cn=function(e){rn()&&performance.clearMarks(e)};!function(e){e.NT="navigation-timing",e.FP="first-paint",e.FCP="first-contentful-paint",e.LCP="largest-contentful-paint",e.CCP="custom-contentful-paint",e.FID="first-input-delay",e.RL="resource-flow",e.CLS="cumulative-layout-shift",e.FPS="fps",e.ACT="api-complete-time",e.DI="device-information",e.NI="network-information",e.PI="page-information"}(Jt||(Jt={}));var fn,sn=function(e,t){var n;try{if(null===(n=PerformanceObserver.supportedEntryTypes)||void 0===n?void 0:n.includes(e)){var r=new PerformanceObserver((function(e){return e.getEntries().map(t)}));return r.observe({type:e,buffered:!0}),r}}catch(e){throw e}},pn=function(e,t,n){var r;void 0===n&&(n=!0),null===(r=function(){if(rn()){var e=function(e,t){var n=e.domainLookupStart,r=e.domainLookupEnd,o=e.connectStart,i=e.connectEnd,a=e.secureConnectionStart,u=e.requestStart,c=e.responseStart,f=e.responseEnd,s=e.domInteractive,p=e.domContentLoadedEventStart,l=e.domContentLoadedEventEnd,d=e.loadEventStart,v=e.fetchStart;t({dnsLookup:Vt(r-n),initialConnection:Vt(i-o),ssl:a?Vt(i-a):0,ttfb:Vt(c-u),contentDownload:Vt(f-c),domParse:Vt(s-f),deferExecuteDuration:Vt(p-s),domContentLoadedCallback:Vt(l-p),resourceLoad:Vt(d-l),domReady:Vt(l-v),pageLoad:Vt(d-v)})};return new Promise((function(t){var n;if(on()&&(null===(n=PerformanceObserver.supportedEntryTypes)||void 0===n?void 0:n.includes("navigation")))var r=sn("navigation",(function(n){"navigation"===n.entryType&&(r&&r.disconnect(),e(n,t))}));else{var o=performance.getEntriesByType("navigation").length>0?performance.getEntriesByType("navigation")[0]:performance.timing;e(o,t)}}))}console.warn("browser do not support performance")}())||void 0===r||r.then((function(r){var o,i={name:Jt.NT,value:r};o=null==Object?void 0:Object.values(i.value),(Array.isArray(o)?o.every((function(e){return e>=0})):o>=0)&&(e.set(Jt.NT,i),n&&t(i))}))},ln=function(e,t,n){void 0===n&&(n=!0);var r=function(){if(rn()){if(an())return{deviceMemory:"deviceMemory"in navigator?navigator.deviceMemory:0,hardwareConcurrency:"hardwareConcurrency"in navigator?navigator.hardwareConcurrency:0,jsHeapSizeLimit:"memory"in performance?Xt(performance.memory.jsHeapSizeLimit):0,totalJSHeapSize:"memory"in performance?Xt(performance.memory.totalJSHeapSize):0,usedJSHeapSize:"memory"in performance?Xt(performance.memory.usedJSHeapSize):0};console.warn("browser do not support navigator")}else console.warn("browser do not support performance")}(),o={name:Jt.DI,value:r};e.set(Jt.DI,o),n&&t(o)},dn=function(e,t,n){void 0===n&&(n=!0);var r=function(){if(an()){var e="connection"in navigator?navigator.connection:{};return{downlink:e.downlink,effectiveType:e.effectiveType,rtt:e.rtt}}console.warn("browser do not support performance")}(),o={name:Jt.NI,value:r};e.set(Jt.NI,o),n&&t(o)},vn=function(e,t,n){void 0===n&&(n=!0);var r=function(){if(location){var e=location.host,t=location.hostname,n=location.href,r=location.protocol,o=location.origin,i=location.port,a=location.pathname,u=location.search,c=location.hash,f=window.screen,s=f.width,p=f.height;return{host:e,hostname:t,href:n,protocol:r,origin:o,port:i,pathname:a,search:u,hash:c,userAgent:"userAgent"in navigator?navigator.userAgent:"",screenResolution:s+"x"+p}}console.warn("browser do not support location")}(),o={name:Jt.PI,value:r};e.set(Jt.PI,o),n&&t(o)},mn="hidden"===document.visibilityState?0:1/0,hn=function(){return tn((function(e){mn=Math.min(mn,e.timeStamp)}),!0),{get timeStamp(){return mn}}},yn=function(){return(yn=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function gn(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var r=Array(e),o=0;for(t=0;t<n;t++)for(var i=arguments[t],a=0,u=i.length;a<u;a++,o++)r[o]=i[a];return r}function wn(e,t){var n=e.podr,r=e.median,o=e.p10,i=n;n||(i=function(e,t){var n=Math.log(e),r=Math.abs(Math.log(t)-n)/(.9061938024368232*Math.SQRT2),o=-3*r-Math.sqrt(4+r*r);return Math.exp(n+r/2*o)}(r,o));var a,u,c,f=Math.log(r),s=Math.log(i/r),p=Math.sqrt(1-3*s-Math.sqrt((s-3)*(s-3)-8))/2,l=(Math.log(t)-f)/(Math.SQRT2*p);return(1-(u=(a=l)<0?-1:1,c=1/(1+.3275911*(a=Math.abs(a))),u*(1-c*(.254829592+c*(c*(1.421413741+c*(1.061405429*c-1.453152027))-.284496736))*Math.exp(-a*a))))/2}var bn=((fn={})[Jt.FP]={median:3e3,p10:1800},fn[Jt.FCP]={median:3e3,p10:1800},fn[Jt.ACT]={median:3500,p10:2300},fn[Jt.LCP]={median:4e3,p10:2500},fn[Jt.CCP]={median:4e3,p10:2500},fn[Jt.FID]={median:300,p10:100},fn[Jt.CLS]={median:.25,p10:.1},fn),En=function(e,t,n){void 0===n&&(n={});var r=yn(yn({},bn),n)[e];return r?wn(r,t):null},Sn=function(e,t,n,r){var o;void 0===n&&(n=!0),null===(o=new Promise((function(e,t){if(on())var n=sn("paint",(function(t){"first-paint"===t.name&&(n&&n.disconnect(),t.startTime<hn().timeStamp&&e(t))}));else if(rn()){var r=performance.getEntriesByName("first-paint")[0];r&&e(r),t(new Error("browser has no fp"))}else t(new Error("browser do not support performance"))})))||void 0===o||o.then((function(o){var i={name:Jt.FP,value:Vt(o.startTime,2),score:En(Jt.FP,o.startTime,r)};e.set(Jt.FP,i),n&&t(i)})).catch((function(e){console.error(e)}))},Cn=function(e,t,n,r){var o;void 0===n&&(n=!0),null===(o=new Promise((function(e,t){if(on())var n=sn("paint",(function(t){"first-contentful-paint"===t.name&&(n&&n.disconnect(),t.startTime<hn().timeStamp&&e(t))}));else if(rn()){var r=performance.getEntriesByName("first-contentful-paint")[0];r&&e(r),t(new Error("browser has no fcp"))}else t(new Error("browser do not support performance"))})))||void 0===o||o.then((function(o){var i={name:Jt.FCP,value:Vt(o.startTime,2),score:En(Jt.FCP,o.startTime,r)};e.set(Jt.FCP,i),n&&t(i)})).catch((function(e){console.error(e)}))},xn=function(e,t,n,r){var o;void 0===n&&(n=!0),null===(o=function(){if(on()){var e=hn();return new Promise((function(t){var n=function(n){n.startTime<e.timeStamp&&(r&&r.disconnect(),t(n))},r=sn("first-input",n);r&&tn((function(){(null==r?void 0:r.takeRecords)&&r.takeRecords().map(n),r.disconnect()}),!0)}))}console.warn("browser do not support performanceObserver")}())||void 0===o||o.then((function(o){var i,a={name:Jt.FID,value:{eventName:o.name,targetCls:null===(i=o.target)||void 0===i?void 0:i.className,startTime:Vt(o.startTime,2),delay:Vt(o.processingStart-o.startTime,2),eventHandleTime:Vt(o.processingEnd-o.processingStart,2)},score:En(Jt.FID,Vt(o.processingStart-o.startTime,2),r)};e.set(Jt.FID,a),n&&t(a)}))},Pn=function(e,t,n,r){void 0===n&&(n=!0);var o={value:{}},i=function(e){if(on()){var t=hn();return sn("largest-contentful-paint",(function(n){n.startTime<t.timeStamp&&(e.value=n)}))}console.warn("browser do not support performanceObserver")}(o),a=function(){if(i&&(i.takeRecords&&i.takeRecords().forEach((function(e){var t=hn();e.startTime<t.timeStamp&&(o.value=e)})),i.disconnect(),!e.has(Jt.LCP))){var a=o.value,u={name:Jt.LCP,value:Vt(a.startTime,2),score:En(Jt.LCP,a.startTime,r)};e.set(Jt.LCP,u),n&&t(u)}};tn(a,!0),["click","keydown"].forEach((function(e){addEventListener(e,a,{once:!0,capture:!0})}))},On=function(e){return t=e,new Promise((function(e){var n=0,r=+new Date,o=[],i=null,a=function(){var u=+new Date;if(n+=1,u>1e3+r){var c=Math.round(n/((u-r)/1e3));o.push(c),n=0,r=+new Date,o.length>t?(cancelAnimationFrame(i),e(Vt(o.reduce((function(e,t){return e+=t}),0)/o.length,2))):i=requestAnimationFrame(a)}else i=requestAnimationFrame(a)};a()}));var t},Tn=function(e,t,n,r){void 0===n&&(n=!0);var o={value:0},i=function(e){if(on()){return sn("layout-shift",(function(t){t.hadRecentInput||(e.value+=t.value)}))}console.warn("browser do not support performanceObserver")}(o);tn((function(){(null==i?void 0:i.takeRecords)&&i.takeRecords().map((function(e){e.hadRecentInput||(o.value+=e.value)})),null==i||i.disconnect();var a={name:Jt.CLS,value:Vt(o.value),score:En(Jt.CLS,o.value,r)};e.set(Jt.CLS,a),n&&t(a)}),!0)};var Mn=function(e){return decodeURIComponent(null==e?void 0:e.replace((null===location||void 0===location?void 0:location.protocol)+"//"+(null===location||void 0===location?void 0:location.host),""))},Ln=Mn(location.href),jn=function(e){window.addEventListener("hashchange",(function(t){e(t)})),window.addEventListener("popstate",(function(t){e(t)})),function(e){if(window.history){var t=history.pushState,n=history.replaceState;history.pushState=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];e&&e.apply(void 0,gn(n,["pushState"])),t.apply(window.history,n)},history.replaceState=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];e&&e.apply(void 0,gn(t,["replaceState"])),n.apply(window.history,t)}}}((function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=Mn(null==t?void 0:t[2]);Ln!==r&&e()}))},_n=!0;jn((function(){_n=!1}));var An,Rn,In=function(){return{get state(){return _n}}},Fn=function(e,t){if(t){var n=e.href.indexOf("#");if(n<0)return"";var r=e.href.slice(n+1),o=r.indexOf("?");return o<0?r:r.slice(0,o)}return e.pathname.replace(/\/$/,"")},kn={hasStoreMetrics:!1,queue:[]},Dn=[],Nn=!1,qn=!0,Hn=function(e,t,n,r){var o;o=e===Jt.ACT?{name:e,value:t,score:En(e,t.time,r)}:e===Jt.CCP?{name:e,value:t,score:En(e,t,r)}:{name:e,value:t},n.set(e,o)},Bn=function(e,t){setTimeout((function(){var n=Array.from(document.querySelectorAll("img")).filter((function(e){return!e.complete&&e.src}));if(n.length>0){var r=0;n.forEach((function(o){o.addEventListener("load",(function(){(r+=1)===n.length&&(Hn(Jt.CCP,performance.now(),e,t),Hn(Jt.RL,performance.getEntriesByType("resource"),e,t))})),o.addEventListener("error",(function(){(r+=1)===n.length&&(Hn(Jt.CCP,performance.now(),e,t),Hn(Jt.RL,performance.getEntriesByType("resource"),e,t))}))}))}else Hn(Jt.CCP,performance.now(),e,t),Hn(Jt.RL,performance.getEntriesByType("resource"),e,t)}))},zn=function(e,t,n,r){if(rn()){var o=Fn(location,n);if(In().state){var i=Zt(e);en(r,i)||(t&&t[o]?t[o].some((function(e){return i===e}))&&kn.queue.push(i):Nn||kn.queue.push(i))}}else console.warn("browser do not support performance")},Wn=function(e,t,n,r,o,i){if(rn()){var a=Fn(location,r);if(In().state){var u,c=Zt(e);if(!en(o,c))if(Dn.push(c),t&&t[a]){if(Yt(kn.queue,Dn)&&!kn.hasStoreMetrics)console.log("api list = ",kn.queue),kn.hasStoreMetrics=!0,(u=performance.now())<hn().timeStamp&&(Hn(Jt.ACT,{time:u,remoteApis:kn.queue},n,i),Bn(n,i))}else if(Yt(kn.queue,Dn)&&!kn.hasStoreMetrics&&Nn)console.log("api list = ",kn.queue),kn.hasStoreMetrics=!0,(u=performance.now())<hn().timeStamp&&(Hn(Jt.ACT,{time:u,remoteApis:kn.queue},n,i),Bn(n,i))}}else console.warn("browser do not support performance")},$n=function(e,t){if(qn){var n=e.get(Jt.ACT),r=e.get(Jt.CCP),o=e.get(Jt.RL);n&&r&&n.value.time<r.value&&(t(n),t(r),o&&t(o)),!n&&r&&(t(r),o&&t(o)),qn=!1}},Un=function(e,t,n,r,o,i,a,u,c){addEventListener(n?"custom-contentful-paint":"pageshow",(function(){In().state&&(Nn=!0,rn()&&performance.now()<hn().timeStamp&&(function(e,t){if(!e||0===e.length)return!1;if(!t||0===t.length)return!1;if(e.length!==t.length)return!1;var n=e.sort(),r=t.sort();return n.join()===r.join()}(kn.queue,Dn)&&!kn.hasStoreMetrics&&(console.log("api list = ",kn.queue),kn.hasStoreMetrics=!0,Hn(Jt.ACT,{time:performance.now(),remoteApis:kn.queue},e,c)),Bn(e,c)))}),{once:!0,capture:!0}),u&&(Qt((function(){return $n(e,t)})),tn((function(){return $n(e,t)}),!0),jn((function(){return $n(e,t)})),function(e,t){setTimeout(e,t)}((function(){return $n(e,t)}),a)),function(e,t){if("XMLHttpRequest"in window&&!window.__monitor_xhr__){var n=window.XMLHttpRequest,r=n.prototype.open;window.__monitor_xhr__=!0,n.prototype.open=function(){for(var n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];e&&e(n[1]),r.apply(this,n),this.addEventListener("loadend",(function(){t&&t(n[1])}))}}}((function(e){return zn(e,r,o,i)}),(function(t){return Wn(t,r,e,o,i,c)})),function(e,t){if("fetch"in window&&!window.__monitor_fetch__){var n=window.fetch;window.__monitor_fetch__=!0,window.fetch=function(r,o){return e&&e(r,o),n.call(window,r,o).then((function(e){return t&&t(r,o),e}),(function(e){throw e}))}}}((function(e){return zn(e,r,o,i)}),(function(t){return Wn(t,r,e,o,i,c)}))},Gn=function(){function e(e){var t=this,n=e.appId,r=e.version,o=e.reportCallback,i=e.immediately,a=void 0!==i&&i,u=e.isCustomEvent,c=void 0!==u&&u,f=e.logFpsCount,s=void 0===f?5:f,p=e.apiConfig,l=void 0===p?{}:p,d=e.hashHistory,v=void 0===d||d,m=e.excludeRemotePath,h=void 0===m?[]:m,y=e.maxWaitCCPDuration,g=void 0===y?3e4:y,w=e.scoreConfig,b=void 0===w?{}:w;this.immediately=a;var E,S="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}));window.__monitor_sessionId__=S,Rn=function(e,t,n,r){return function(o){var i={sessionId:e,appId:t,version:n,data:o,timestamp:+new Date};"requestIdleCallback"in window?window.requestIdleCallback((function(){r(i)}),{timeout:3e3}):r(i)}}(S,n,r,o),An=new nn,vn(An,Rn,a),dn(An,Rn,a),ln(An,Rn,a),Tn(An,Rn,a,b),Pn(An,Rn,a,b),Un(An,Rn,c,l,v,h,g,a,b),addEventListener(c?"custom-contentful-paint":"pageshow",(function(){Sn(An,Rn,a,b),Cn(An,Rn,a,b)}),{once:!0,capture:!0}),E=function(){pn(An,Rn,a),xn(An,Rn,a,b),function(e,t,n,r){void 0===r&&(r=!0),On(n).then((function(n){var o={name:Jt.FPS,value:n};e.set(Jt.FPS,o),r&&t(o)}))}(An,Rn,s,a)},"complete"===document.readyState?setTimeout(E):addEventListener("pageshow",E),[Qt,Kt,tn].forEach((function(e){e((function(){var e=t.getCurrentMetrics();Object.keys(e).length>0&&!a&&Rn(e)}))}))}return e.prototype.getCurrentMetrics=function(){return An.getValues()},e.dispatchCustomEvent=function(){var e=document.createEvent("Events");e.initEvent("custom-contentful-paint",!1,!0),document.dispatchEvent(e)},e.prototype.setStartMark=function(e){un(e+"_start")},e.prototype.setEndMark=function(e){var t;if(un(e+"_end"),function(e){if(rn())return performance.getEntriesByName(e).length>0;console.error("browser do not support performance")}(e+"_start")){var n=function(e,t){if(rn())return performance.measure(e,t+"_start",t+"_end"),performance.getEntriesByName(e).pop();console.error("browser do not support performance")}(e+"Metrics",e);this.clearMark(e);var r={name:e+"Metrics",value:n};An.set(e+"Metrics",r),this.immediately&&Rn(r)}else{n=null===(t=function(e){if(rn())return performance.getEntriesByName(e).pop();console.error("browser do not support performance")}(e+"_end"))||void 0===t?void 0:t.startTime;this.clearMark(e);r={name:e+"Metrics",value:n};An.set(e+"Metrics",r),this.immediately&&Rn(r)}},e.prototype.clearMark=function(e){cn(e+"_start"),cn(e+"_end")},e.prototype.customContentfulPaint=function(){setTimeout((function(){e.dispatchCustomEvent()}))},e}();return e.WebVitals=Gn,Object.defineProperty(e,"__esModule",{value:!0}),e}({});
