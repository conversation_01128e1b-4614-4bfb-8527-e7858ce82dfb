<template>
  <div class="container">
    <Tab :tablist="tablist" :tabIndex="tabIndex" @changeTab="changeTab"></Tab>
    <!-- 值班看板 -->
    <div class="con" v-if="tabIndex == 0">
      <kanboard></kanboard>
    </div>
    <!-- 排班管理 -->
    <div class="con" v-if="tabIndex == 1">
      <management></management>
    </div>
    <!-- 交接日志 -->
    <div class="con" v-if="tabIndex == 2">
      <handover></handover>
    </div>
    <!-- 统计分析 -->
    <div class="con" v-if="tabIndex == 3">
      <analysis></analysis>
    </div>
  </div>
</template>

<script>
import Tab from "@/views/serve/components/Tab.vue";
import kanboard from "@/views/serve/duty/dutyKanboard/index.vue";
import management from "@/views/serve/duty/dutyManagement/index.vue";
import handover from "@/views/serve/duty/handoverLog/index.vue";
import analysis from "@/views/serve/duty/analysis/index.vue";
export default {
  components: { Tab, kanboard, management, handover, analysis },
  data() {
    return {
      tabIndex: 0,
      tablist: [
        { name: "值班看板" },
        { name: "排班管理" },
        { name: "交接日志" },
        { name: "统计分析" },
      ],
    };
  },
  methods: {
    changeTab(e) {
      this.tabIndex = e.index;
      // this.$router.push({ path: e.path });
    },
  },
};
</script>

<style lang="scss" scoped>
.con {
  margin-top: 20px;
}
</style>
