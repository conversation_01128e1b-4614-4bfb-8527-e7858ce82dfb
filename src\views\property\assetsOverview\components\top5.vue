<template>
  <div class="flex-c">
    <div style="width: 50%; height: 230px" id="yzysy"></div>
    <div style="width: 50%; height: 230px" id="yzysy2"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  props: {
    data1: {
      type: Array,
      default: () => [],
    },
    data2: {
      type: Array,
      default: () => [],
    },
  },
  mounted() {
    this.$nextTick(() => { // 确保 DOM 更新后再初始化图表
      if (this.data1 && document.getElementById("yzysy")) {
        this.initChart(this.data1);
      }
      if (this.data2 && document.getElementById("yzysy2")) {
        this.initChart2(this.data2);
      }
    });
  },
  methods: {
    initChart(data) {
      let chart = echarts.init(document.getElementById("yzysy"));
      let option = {
        color: ["#86DF6C", "#249EFF", "#21CCFF", "#0E42D2", "#846BCE"],
        title: {
          text: "云资源使用",
          // textAlign: "center",
          top: "center",
          left: "20.6%",
          textStyle: {
            fontSize: 14,
          },
        },
        tooltip: {
          trigger: "item",
        },
        legend: {
          right: "12%",
          top: "center",
          icon: 'circle',
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 18,
          orient: "vertical",
          formatter: (name) => {
            let value = data.find((x) => x.name == name)?.value || 0; // 确保 value 存在
            return `{a|${name}}{b|${value}台}`;
          },
          textStyle: {
            padding: [0, 0, 0, 6],
            rich: {
              a: {
                width: 80,
              },
            },
          },
        },
        series: [
          {
            type: "pie",
            data: data.map((x) => ({ name: x.name, value: x.value })), // 确保 data 格式正确
            radius: ["46%", "70%"],
            center: ["28%", "50%"],
            label: {
              show: true,
              position: "outside",
              formatter: "{d}%",
            },
            labelLine: {
              show: true,
              length: 10,
              length2: 10,
            },
          },
        ],
      };
      chart.setOption(option);
    },
    initChart2(data) {
      let chart = echarts.init(document.getElementById("yzysy2"));
      let option = {
        color: ["#F98E1B", "#3AA1FF"],
        tooltip: {
          trigger: "axis",
        },
        grid: {
          left: "2%",
          right: 0,
          bottom:'10%',
          top: "20%",
          containLabel: true,
        },
        legend: {
          icon: "roundRect",
          itemWidth: 16,
          itemHeight: 10,
          itemGap: 20,
          right: 0,
        },
        xAxis: {
          type: "category",
          data: data.map((x) => x.name),
          boundaryGap: true,
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#858D9D",
          },
          axisLine: {
            lineStyle: {
              color: "#DEE3E9",
            },
          },
        },
        yAxis: {
          type: "value",
          name: "单位：%",
          splitNumber: 4,
          axisLabel: {
            color: "#667085",
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: "#DEE3E9",
              type: "dotted",
            },
          },
        },
        series: [
          {
            name: "CPU平均使用率",
            type: "line",
            smooth: false,
            showSymbol: false,
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#F98E1B",
                },
                {
                  offset: 1,
                  color: " rgba(249,142,27,0)",
                },
              ]),
            },
            emphasis: {
              focus: "series",
            },
            data: data.map((x) => x.value1),
          },
          {
            name: "内存平均使用率",
            type: "line",
            smooth: false,
            showSymbol: false,
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#3AA1FF",
                },
                {
                  offset: 1,
                  color: "rgba(59,161,255,0)",
                },
              ]),
            },
            data: data.map((x) => x.value2),
          },
        ],
      };
      chart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
