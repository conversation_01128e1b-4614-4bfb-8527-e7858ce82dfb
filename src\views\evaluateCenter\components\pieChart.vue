<template>
  <div class="chart-wrapper">
    <div class="chart-title">工单平均完成时间</div>
    <div id="pieChart"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  name: 'pieChart',
  props: {
    chartData: {
      required: true,
    }
  },
  data() {
    return {
      chart: null,
      // chartData: [
      //   { value: 100, name: '时间区间一', ratio: 20},
      //   { value: 180, name: '时间区间二', ratio: 36 },
      //   { value: 120, name: '时间区间三', ratio: 24 },
      //   { value: 150, name: '时间区间四', ratio: 30 },
      //   { value: 50, name: '时间区间五', ratio: 10 }
      // ],
      option: {}
    }
  },
  mounted() {
    let that = this
    setTimeout(() => {
      that.initChart()
    })
  },
  methods: {
    initChart() {
      let chartData = this.chartData
      this.chart = echarts.init(document.getElementById('pieChart'))
      this.option = {
        color: [
          '#86DF6C',
          '#0E42D2',
          '#249EFF',
          '#21CCFF',
          '#64EADC',
          '#81D3F8'
        ],
        tooltip: {
          trigger: 'item',
        },
        legend: {
          icon: 'circle',
          top: '10%',
          left: '50%',
          orient: 'vertical',
          formatter(name) {
            let target;
            chartData.forEach((item) => {
              if (item.name === name) {
                target = item;
              }
            });
            return `{a| ${name} } {b| ${target.value}个 } {c| ${target.ratio}}`;
          },
          textStyle: {
            align: 'right',
            rich: {
              a: {
                color: '#4E5969',
                fontSize: 12
              },
              b: {
                color: '#4E5969',
                fontSize: 12
              },
              c: {
                color: '#4E5969',
                fontSize: 12
              }
            }
          }
        },
        grid: {
          left: '8%',
          right: '4%',
          bottom: '4%',
        },
        series: [
          {
            name: '工单平均完成时间',
            type: 'pie',
            radius: ['55%', '80%'],
            center: ['25%', '45%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 40,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: chartData
          }
        ]
      }
      this.chart.setOption(this.option)
    }
  }
}
</script>

<style scoped lang="scss">
.chart-title {
  font-weight: 400;
  font-size: 18px;
  color: #1D2129;
  position: relative;
  padding-left: 10px;
}
.chart-title:before {
  content: '';
  display: block;
  border-radius: 3px;
  width: 3px;
  height: 13px;
  background: #0057FE;
  left: 0px;
  top: 6px;
  position: absolute;
}
#pieChart {
  width: 550px;
  height: 200px;
  margin-top: 10px;
}
</style>
