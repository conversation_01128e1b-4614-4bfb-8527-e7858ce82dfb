<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2024-11-25 16:25:55
 * @LastEditors: wjb
 * @LastEditTime: 2025-05-09 14:55:07
-->
<template>
  <div></div>
</template>

<script>
import { getDddlUrl } from "@/api/login";

export default {
  name: "index",
  data() {
    return {};
  },
  computed: {},
  created() {
    getDddlUrl({ name: "电信云平台" }).then((res) => {
      if (res.code == 200) {
        window.open(res.msg);
      }
    });
  },
  methods: {},
  watch: {},
};
</script>

<style scoped>
</style>
