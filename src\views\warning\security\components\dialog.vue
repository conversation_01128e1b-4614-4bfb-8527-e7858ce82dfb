<template>
  <el-dialog :title="title" :visible="show" width="65%" @close="close">
    <el-form
      ref="myForm"
      :model="myForm"
      label-width="80px"
      label-position="top"
      inline
    >
      <div class="card">
        <div class="cardTitle">基本信息</div>
        <div class="itemList flex-c">
          <el-form-item label="事件名称" class="item_grid_3">
            <el-input
              v-model="myForm.sjmc"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="事件等级" class="item_grid_3">
            <el-select
              v-model="myForm.sjdj"
              clearable
              size="small"
              style="width: 100%"
              :disabled="disabled"
            >
              <el-option
                v-for="(item, i) in sjdjOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发现时间" class="item_grid_3">
            <el-date-picker
              v-model="myForm.fxsj"
              type="date"
              placeholder="选择日期"
              :disabled="disabled"
              size="small"
              style="width: 100%"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="事件描述" class="item_grid_3">
            <el-input
              v-model="myForm.sjms"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="目标对象URL" class="item_grid_3">
            <el-input
              v-model="myForm.mbdjurl"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="目标对象域名" class="item_grid_3">
            <el-input
              v-model="myForm.mbdxym"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="目标对象IP" class="item_grid_3">
            <el-input
              v-model="myForm.mbdxip"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="目标对象端口" class="item_grid_3">
            <el-input
              v-model="myForm.mbdxdk"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="目标对象所在地" class="item_grid_3">
            <el-select
              v-model="myForm.mbdxszd"
              clearable
              size="small"
              style="width: 100%"
              :disabled="disabled"
            >
              <el-option
                v-for="(item, i) in mbdxszdOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="攻击者IP" class="item_grid_3">
            <el-input
              v-model="myForm.gjzip"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="攻击者端口" class="item_grid_3">
            <el-input
              v-model="myForm.gjzdk"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="攻击者所在地" class="item_grid_3">
            <el-select
              v-model="myForm.gjzszd"
              clearable
              size="small"
              style="width: 100%"
              :disabled="disabled"
            >
              <el-option
                v-for="(item, i) in gjzszdOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发现厂商" class="item_grid_3">
            <el-select
              v-model="myForm.fxcs"
              clearable
              size="small"
              style="width: 100%"
              :disabled="disabled"
            >
              <el-option
                v-for="(item, i) in fxcsOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="事件类别" class="item_grid_3">
            <el-select
              v-model="myForm.sjlb"
              clearable
              size="small"
              style="width: 100%"
              :disabled="disabled"
            >
              <el-option
                v-for="(item, i) in sjlbOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="子类别" class="item_grid_3">
            <el-select
              v-model="myForm.zlb"
              clearable
              size="small"
              style="width: 100%"
              :disabled="disabled"
            >
              <el-option
                v-for="(item, i) in zlbOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="事件来源" class="item_grid_3">
            <el-select
              v-model="myForm.sjly"
              clearable
              size="small"
              style="width: 100%"
              :disabled="disabled"
            >
              <el-option
                v-for="(item, i) in sjlyOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="受害者" class="item_grid_3">
            <el-input
              v-model="myForm.shz"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="填报单位" class="item_grid_3">
            <el-input
              v-model="myForm.tbdw"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="备注" class="item_grid_3">
            <el-input
              v-model="myForm.bz"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="card">
        <div class="cardTitle">单位归属</div>
        <div class="itemList flex-c">
          <el-form-item label="单位名称" class="item_grid_3">
            <el-input
              v-model="myForm.dwmc"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="系统名称" class="item_grid_3">
            <el-input
              v-model="myForm.xtmc"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="单位级别" class="item_grid_3">
            <el-select
              v-model="myForm.dwjb"
              clearable
              size="small"
              style="width: 100%"
              :disabled="disabled"
            >
              <el-option
                v-for="(item, i) in dwjbOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="单位性质" class="item_grid_3">
            <el-input
              v-model="myForm.dwxz"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="行业类别" class="item_grid_3">
            <el-select
              v-model="myForm.hylb"
              clearable
              size="small"
              style="width: 100%"
              :disabled="disabled"
            >
              <el-option
                v-for="(item, i) in hylbOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
      </div>
      <div class="card">
        <div class="cardTitle">描述信息</div>
        <el-form-item label="举证信息" class="item_grid_1">
          <el-input
            type="textarea"
            v-model="myForm.jzxx"
            :rows="3"
            placeholder="请输入"
            :disabled="disabled"
          ></el-input>
        </el-form-item>
        <el-form-item label="描述信息" class="item_grid_1">
          <el-input
            type="textarea"
            v-model="myForm.msxx"
            :rows="3"
            placeholder="请输入"
            :disabled="disabled"
          ></el-input>
        </el-form-item>
        <el-form-item label="原始事件" class="item_grid_1">
          <el-input
            type="textarea"
            v-model="myForm.yssj"
            :rows="3"
            placeholder="请输入"
            :disabled="disabled"
          ></el-input>
        </el-form-item>
        <el-form-item label="文件上传" class="item_grid_1">
          <FileUpload :limit="5" v-model="myForm.wj"></FileUpload>
        </el-form-item>
        <el-form-item label="图片上传" class="item_grid_1">
          <ImgUpload :limit="20" v-model="myForm.tp"></ImgUpload>
        </el-form-item>
      </div>
      <div class="card">
        <div class="cardTitle">更多信息</div>
        <div class="itemList flex-c">
          <el-form-item label="目的主机名" class="item_grid_3">
            <el-input
              v-model="myForm.mdzjm"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="目的端口" class="item_grid_3">
            <el-input
              v-model="myForm.mddk"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="置信度" class="item_grid_3">
            <el-input
              v-model="myForm.zxd"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="数据流方向" class="item_grid_3">
            <el-select
              v-model="myForm.sjlfx"
              clearable
              size="small"
              style="width: 100%"
              :disabled="disabled"
            >
              <el-option
                v-for="(item, i) in sjlfxOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="请求头" class="item_grid_3">
            <el-input
              v-model="myForm.qqt"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="请求方法" class="item_grid_3">
            <el-input
              v-model="myForm.qqff"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="请求响应吗" class="item_grid_3">
            <el-input
              v-model="myForm.qqxym"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="URI" class="item_grid_3">
            <el-input
              v-model="myForm.uri"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="事件ID" class="item_grid_3">
            <el-input
              v-model="myForm.sjid"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="模型ID" class="item_grid_3">
            <el-input
              v-model="myForm.mxid"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="模型名称" class="item_grid_3">
            <el-input
              v-model="myForm.mxmc"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="请求域名" class="item_grid_3">
            <el-input
              v-model="myForm.qqym"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="目的IP" class="item_grid_3">
            <el-input
              v-model="myForm.mdip"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
          <el-form-item label="结束时间" class="item_grid_3">
            <el-date-picker
              v-model="myForm.jssj"
              type="date"
              placeholder="选择日期"
              :disabled="disabled"
              size="small"
              style="width: 100%"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="最后更新时间" class="item_grid_3">
            <el-date-picker
              v-model="myForm.gxsj"
              type="date"
              placeholder="选择日期"
              :disabled="disabled"
              size="small"
              style="width: 100%"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="请求Body" class="item_grid_3">
            <el-input
              v-model="myForm.qqbody"
              size="small"
              clearable
              placeholder="请输入"
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </div>
        <el-form-item label="请求响应内容" class="item_grid_1">
          <el-input
            type="textarea"
            v-model="myForm.qqxynr"
            :rows="3"
            placeholder="请输入"
            :disabled="disabled"
          ></el-input>
        </el-form-item>
      </div>
      <span class="footer">
        <el-button @click="close" size="small">取 消</el-button>
        <el-button type="primary" @click="close" size="small">保 存</el-button>
      </span>
    </el-form>
  </el-dialog>
</template>

<script>
import FileUpload from "@/components/FileUpload/index.vue";
import ImgUpload from "@/components/ImageUpload/index.vue";

export default {
  components: { FileUpload, ImgUpload },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    form: {
      type: Object,
      default: () => {},
    },
    title: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      myForm: {},
      sjdjOptions: [],
      mbdxszdOptions: [],
      gjzszdOptions: [],
      fxcsOptions: [],
      sjlbOptions: [],
      zlbOptions: [],
      sjlyOptions: [],
      dwjbOptions: [],
      hylbOptions: [],
      sjlfxOptions: [],
    };
  },
  mounted() {
    if (this.form) {
      this.myForm = JSON.parse(JSON.stringify(this.form));
    }
  },
  methods: {
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  padding: 20px 20px;
  padding-bottom: 0;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  // margin-bottom: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-bottom: 10px;
    padding-left: 18px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 2px;
      left: 0;
      width: 10px;
      height: 20px;
      background: url("~@/assets/images/cardTitle_icon.png");
      background-size: 100% 100%;
    }
  }
}
.item_grid_1 {
  width: 100%;
  padding: 0 36px;
}
.itemList {
  flex-wrap: wrap;
  padding: 0 20px;
  box-sizing: border-box;
  .item_grid_3 {
    width: 30%;
    margin-left: 16px;
  }
}
.footer {
  margin-top: 20px;
  border-top: solid 1px #e5e6eb;
  padding: 20px 40px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
::v-deep .el-dialog {
  border-radius: 12px;
  overflow: hidden;
  // .el-dialog__header {
  //   padding: 0;
  // }
  .el-dialog__body {
    padding: 0;
    max-height: 640px;
    overflow-y: scroll;
    padding-bottom: 20px;
    .el-form-item__label {
      padding: 0;
      line-height: 30px;
    }
    .el-form-item {
      margin-bottom: 10px;
    }
  }
}
</style>
