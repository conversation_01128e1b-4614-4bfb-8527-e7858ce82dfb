<template>
  <div class="container knowledge_base">
    <div class="card" v-loading="rightLoading">
      <div class="title_area">
        <div class="title_left">运维知识库</div>
        <div class="title_right">
          <el-button size="small" class="mr10" @click="openSetDialog">
            分类设置
          </el-button>
          <el-button
            icon="el-icon-plus"
            type="primary"
            size="small"
            class="mr10"
            @click="openDialog(0)"
          >
            新增知识
          </el-button>
        </div>
      </div>
      <splitpanes
        :horizontal="this.$store.getters.device === 'mobile'"
        class="default-theme"
      >
        <pane size="16">
          <div class="left_menu_area">
            <left-com
              ref="leftMenuRef"
              @item-click="handleMenuClick"
              @treeChangeSd="treeChangeSd"
              @changeCategory="changeCategory"
              :active-item-id="currentActiveId"
            >
            </left-com>
          </div>
        </pane>
        <pane size="84">
          <div class="right_content_area">
            <template v-if="showAddForm.title">
              <div class="content_area_title">
                <span class="mr10">{{ showAddForm.title }}</span>
                <el-button size="small" type="primary" @click="openDialog(1)">
                  编辑文档
                </el-button>
                <el-button size="small" @click="deleteLibrary">
                  删除文档
                </el-button>
              </div>
              <div v-html="showAddForm.describe"></div>
              <div v-if="!showAddForm.filePath"></div>
              <el-image
                style="max-width: 300px"
                :src="showAddForm.filePath"
                :preview-src-list="[showAddForm.filePath]"
                v-else-if="getFileType(showAddForm.filePath) === 'img'"
              >
              </el-image>
              <div
                class="is_other_type"
                @click="openFile(showAddForm.filePath)"
                v-else
              >
                <img
                  :src="getFileIcon(getFileTypeByUrl(showAddForm.filePath))"
                  :alt="showAddForm.fileName"
                />
                <span>{{ showAddForm.fileName }}</span>
              </div>
            </template>
            <template v-else>
              <el-empty :image-size="200"></el-empty>
            </template>
          </div>
        </pane>
      </splitpanes>
    </div>

    <el-dialog
      :title="dialogTitle"
      :visible.sync="open"
      width="780px"
      append-to-body
      custom-class="knowledge_base-dialog"
    >
      <el-form
        ref="addForm"
        :model="addForm"
        :rules="rules"
        label-width="80px"
        label-position="top"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <FileUpload
              ref="fileUploadRef"
              :limit="1"
              v-model="importFile"
              btnName="导入"
              :fileSize="20"
              :fileType="['doc', 'docx']"
              :showFileList="false"
              action="/tyywpt/tTyywZskNew/importWordDocument"
              @importSuccess="importSuccess"
            ></FileUpload>
          </el-col>
          <el-col :span="12">
            <el-form-item label="知识库类别" prop="category">
              <el-radio-group v-model="addForm.category" @change="changeLb">
                <el-radio label="general">通用知识库</el-radio>
                <el-radio label="project">项目知识库</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="属地" prop="region">
              <el-select
                v-model="addForm.region"
                placeholder="请选择属地"
                size="small"
                style="width: 100%"
                @change="changeSd"
              >
                <el-option
                  v-for="item in sdOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="知识分类" prop="typeList">
              <el-cascader
                v-model="addForm.typeList"
                :options="processedMenuData"
                clearable
                class="w100p"
                :props="cascaderProps"
                placeholder="请选择知识分类"
              ></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标题" prop="title">
              <el-input
                v-model="addForm.title"
                placeholder="请输入标题"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="描述" prop="describe">
              <editor
                v-model="addForm.describe"
                :min-height="192"
                :key="editorKey"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件">
              <FileUpload :limit="1" v-model="addForm.fjList"></FileUpload>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="lookFlag">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="addForm.createTime"
                type="datetime"
                placeholder="创建时间"
                disabled
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";
import LeftCom from "./components/leftMenu.vue";
import {
  editLibrary,
  getLibraryDetail,
  getLibraryTree,
  getProjectLibraryTree,
  deleteLibrary,
} from "@/api/serve/knowledgeBase.js";
import { listSd } from "@/api/property/projectManage";

export default {
  components: { Splitpanes, Pane, LeftCom },
  data() {
    return {
      currentActiveId: "",
      processedMenuData: [],
      selectedMenu: null,
      cascaderProps: {
        value: "treeId",
        label: "treeName",
        children: "children",
      },
      lookFlag: false,
      treeSd: "",
      treeCategory: "general",
      sdOptions: [],
      addForm: {
        category: "general",
        region: "",
        typeList: [],
        title: "",
        describe: "",
        fjList: "",
      },
      rules: {
        category: [
          { required: true, message: "知识库类别不能为空", trigger: "change" },
        ],
        region: [
          { required: true, message: "属地不能为空", trigger: "change" },
        ],
        typeList: [
          { required: true, message: "知识分类不能为空", trigger: "change" },
        ],
        title: [{ required: true, message: "标题不能为空", trigger: "blur" }],
        describe: [
          { required: true, message: "描述不能为空", trigger: "blur" },
        ],
      },
      dialogTitle: "",
      open: false,
      importFile: null,
      editorKey: 1,
      showAddForm: {},
      rightLoading: false,
    };
  },
  computed: {},
  methods: {
    async getListSd() {
      const res = await listSd();
      this.sdOptions = res.data.map((item) => {
        return {
          label: item,
          value: item,
        };
      });
    },
    treeChangeSd(sd) {
      this.treeSd = sd;
    },
    changeCategory(val) {
      if (val == "first") {
        this.treeCategory = "general";
      } else if (val == "second") {
        this.treeCategory = "project";
      }
    },
    async getLibraryTree() {
      const res = await getLibraryTree({
        region: this.addForm.region,
        needLibrary: false,
      });
      if (res.code === 200) {
        this.processedMenuData = res.data || [];
      }
    },

    async getProjectLibraryTree() {
      const res = await getProjectLibraryTree({
        region: this.addForm.region,
        needLibrary: false,
      });
      if (res.code === 200) {
        this.processedMenuData = res.data || [];
      }
    },
    changeSd() {
      if (this.addForm.category == "general") {
        this.getLibraryTree();
      } else if (this.addForm.category == "project") {
        this.getProjectLibraryTree();
      }
    },
    changeLb() {
      this.changeSd();
    },
    openSetDialog() {
      this.$refs.leftMenuRef.openSetDialog();
    },
    importSuccess(res) {
      if (res.data) {
        this.addForm.describe = res.data;
        this.importFile = null;
        // 清除文件上传组件的文件列表，避免"只能上传一个文件"的错误
        this.$nextTick(() => {
          if (this.$refs.fileUploadRef && this.$refs.fileUploadRef.$refs.fileUpload) {
            this.$refs.fileUploadRef.$refs.fileUpload.clearFiles();
          }
        });
      }
    },
    handleMenuClick(item) {
      this.selectedMenu = item;
      this.currentActiveId = item.treeId;
      let id = item.treeId.match(/(?<=library:)[^:]+$/)?.at(0);
      this.rightLoading = true;
      getLibraryDetail(id).then((res) => {
        this.rightLoading = false;
        if (res.code === 200) {
          this.showAddForm = { ...res.data };
        }
      });
    },
    submitForm() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          let query = { ...this.addForm };
          query.id = this.addForm.id || 0;
          query.filePath = this.addForm.fjList || "";
          query.fileName = query.filePath
            ? query.filePath.match(/\/([^\/]+)$/)[1]
            : "";
          query.typeId = query.typeList[query.typeList.length - 1];
          query.typeList = query.typeList.join(",");
          editLibrary(query).then((res) => {
            if (res.code === 200) {
              this.open = false;
              this.$modal.msgSuccess(this.addForm.id ? "修改成功" : "新增成功");
              this.$refs.leftMenuRef.handleClick();
              this.showAddForm = {};
            }
          });
        }
      });
    },
    async openDialog(val) {
      this.dialogTitle = val ? "修改知识库" : "新增知识库";
      if (this.dialogTitle === "新增知识库") {
        this.addForm = {
          category: "general",
          region: "",
          typeList: [],
          title: "",
          describe: "",
          fjList: "",
        };
        this.addForm.region = this.treeSd;
        this.addForm.category = this.treeCategory;
      }
      this.importFile = null;
      // 清除文件上传组件的文件列表
      this.$nextTick(() => {
        if (this.$refs.fileUploadRef && this.$refs.fileUploadRef.$refs.fileUpload) {
          this.$refs.fileUploadRef.$refs.fileUpload.clearFiles();
        }
      });
      if (val) {
        this.addForm = { ...this.showAddForm };
        this.addForm.fjList = this.showAddForm.filePath;
        this.addForm.typeList = this.showAddForm.typeList.split(",");
        this.lookFlag = true;
      } else {
        this.lookFlag = false;
      }
      if (this.addForm.category == "general") {
        await this.getLibraryTree();
      } else if (this.addForm.category == "project") {
        await this.getProjectLibraryTree();
      }
      this.editorKey++;
      this.open = true;
    },
    getFileType(filePath) {
      const extension = this.getExtensionFromPath(filePath);
      const imageExtensions = new Set([
        "jpg",
        "jpeg",
        "png",
        "gif",
        "bmp",
        "webp",
        "svg",
        "tiff",
        "ico",
      ]);
      return imageExtensions.has(extension) ? "img" : "other";
    },
    getExtensionFromPath(path) {
      if (typeof path !== "string" || path.trim() === "") return "";
      const filenameMatch = path.match(/[^/?#]+$/);
      if (!filenameMatch) return "";
      const filename = filenameMatch[0];
      const lastDotIndex = filename.lastIndexOf(".");
      if (
        lastDotIndex === -1 ||
        lastDotIndex === 0 ||
        lastDotIndex === filename.length - 1
      ) {
        return "";
      }
      return filename.slice(lastDotIndex + 1).toLowerCase();
    },

    getFileIcon(type) {
      const iconMap = {
        1: require("@/assets/images/serve/word.png"), // 文档
        2: require("@/assets/images/serve/excel.png"), // 表格
        3: require("@/assets/images/serve/pdf.png"), // PDF
        4: "", // 图片类型不使用图标，直接显示缩略图
      };

      return iconMap[type] || require("@/assets/images/serve/word.png");
    },
    openFile(url) {
      window.open(url, "_blank");
    },
    getFileTypeByUrl(url) {
      const extension = this.getExtensionFromUrl(url);
      const typeMap = {
        doc: 1,
        docx: 1,
        xls: 2,
        xlsx: 2,
        xlsm: 2,
        xlsb: 2,
        pdf: 3,
      };
      return typeMap[extension] || 0;
    },
    getExtensionFromUrl(url) {
      if (typeof url !== "string" || url.trim() === "") return "";
      const filenameMatch = url.match(/[^/?#]+$/);
      if (!filenameMatch) return "";
      const filename = filenameMatch[0];
      const lastDotIndex = filename.lastIndexOf(".");
      if (
        lastDotIndex === -1 ||
        lastDotIndex === 0 ||
        lastDotIndex === filename.length - 1
      ) {
        return "";
      }
      return filename.slice(lastDotIndex + 1).toLowerCase();
    },
    deleteLibrary() {
      // 添加弹框删除提示框弹框
      this.$confirm("确定要删除这个知识库吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteLibrary(this.showAddForm.id).then((res) => {
            if (res.code === 200) {
              this.$modal.msgSuccess("删除成功");
              this.$refs.leftMenuRef.handleClick();
              this.showAddForm = {};
            }
          });
        })
        .catch(() => {
          // 取消删除
        });
    },
  },
  mounted() {
    this.getListSd();
  },
};
</script>

<style lang="scss" scoped>
.card {
  border-radius: 15px;
  padding: 12px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: 100%;
  margin-bottom: 12px;
}

.title_area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eaecf0;
  padding-bottom: 12px;
}

.title_left {
  font-size: 22px;
  color: #1d2129;
  font-weight: 700;
}

.btn_box {
  display: flex;
  align-content: center;
  align-items: center;
  padding: 20px;
}

.left_menu_area {
  padding: 0 10px 20px 0;
}

.right_content_area {
  padding: 30px;
}

.content_area_title {
  font-size: 18px;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
}

.w100p {
  width: 100%;
}

.is_other_type {
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  width: fit-content;
  transition: all 0.3s;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background-color: #f8f9fa;
  }
}
</style>

<style lang="scss">
.knowledge_base {
  height: calc(100vh - 110px);
}
.knowledge_base-dialog {
  border-radius: 10px;

  .el-dialog__header {
    background: #f2f2f2;
    border-radius: 10px 10px 0 0;
  }

  .el-dialog__footer {
    border-top: 1px solid #e5e6eb;
    border-radius: 0 0 10px 10px;
  }
}
.splitpanes__pane {
  overflow-y: auto;
  padding-bottom: 40px;
}
</style>