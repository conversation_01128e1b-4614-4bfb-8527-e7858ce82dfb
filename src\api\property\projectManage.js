import request from "@/utils/request";

// 查询统一运维项目列表
export function listProject(query) {
  return request({
    url: "/tyywpt/tTyywProject/list",
    method: "get",
    params: query,
  });
}

// 获取统一运维项目详细信息
export function getProject(id) {
  return request({
    url: "/tyywpt/tTyywProject/getInfo",
    method: "get",
    params: { id: id },
  });
}

// 新增统一运维平台-应用
export function addProject(data) {
  return request({
    url: "/tyywpt/tTyywProject/add",
    method: "post",
    data: data,
  });
}

// 修改统一运维项目
export function updateProject(data) {
  return request({
    url: "/tyywpt/tTyywProject/edit",
    method: "put",
    data: data,
  });
}

// 删除统一运维项目
export function delProject(ids) {
  return request({
    url: "/tyywpt/tTyywProject/remove",
    method: "delete",
    params: { ids: ids },
  });
}

// 获取用户属地
export function listSd(query) {
  return request({
    url: "/tyywpt/tTyywProject/getUserSd",
    method: "get",
    params: query,
  });
}