<template>
  <div>
    <div class="card" style="width: 100%">
      <div class="cardTitle">值班情统计分析</div>
      <div class="flex-b" style="align-items: flex-start">
        <div class="left">
          <div class="item flex-b" v-for="(item, i) in leftList" :key="i">
            <div>
              <div class="name">{{ item.name }}</div>
              <div class="text">
                {{ item.label }}:{{ item.value }}{{ item.unit }}
              </div>
            </div>
            <div class="num">{{ item.num }}%</div>
          </div>
        </div>
        <div class="center">
          <div class="subtitle">问题分类统计</div>
          <div id="piechart" style="width: 100%; height: 180px"></div>
        </div>
        <div class="right">
          <div class="subtitle">绩效趋势</div>
          <div id="linechart" style="width: 100%; height: 180px"></div>
        </div>
      </div>
    </div>
    <div class="card">
      <div class="cardTitle">值班人员绩效</div>
      <el-table :data="datalist">
        <el-table-column prop="zbry" label="值班人员" align="center" />
        <el-table-column prop="zbcs" label="值班次数" align="center" />
        <el-table-column prop="xyl" label="响应率" align="center" />
        <el-table-column prop="wtjjl" label="问题解决率" align="center" />
        <el-table-column prop="pjxysj" label="平均响应时间" align="center" />
        <el-table-column prop="pf" label="评分" align="center" />
      </el-table>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  components: {},
  data() {
    return {
      leftList: [
        {
          name: "响应效率",
          label: "平均响应时间",
          value: "15",
          unit: "分钟",
          num: 98,
        },
        {
          name: "响应效率",
          label: "本月累计解决",
          value: "42",
          unit: "件",
          num: 92,
        },
      ],
      pieData: [
        { name: "问题一", value: 20 },
        { name: "问题二", value: 20 },
        { name: "问题三", value: 20 },
      ],
      lineData: [
        { name: "6月", value1: 20, value2: 3 },
        { name: "7月", value1: 30, value2: 3 },
        { name: "8月", value1: 15, value2: 3 },
        { name: "9月", value1: 35, value2: 6 },
        { name: "10月", value1: 10, value2: 4 },
        { name: "11月", value1: 12, value2: 3 },
      ],
      datalist: [
        {
          zbry: "王华",
          zbcs: "12",
          xyl: "95%",
          wtjjl: "98%",
          pjxysj: "5分钟",
          pf: "9.5分",
        },
        {
          zbry: "王华",
          zbcs: "12",
          xyl: "95%",
          wtjjl: "98%",
          pjxysj: "5分钟",
          pf: "9.5分",
        },
        {
          zbry: "王华",
          zbcs: "12",
          xyl: "95%",
          wtjjl: "98%",
          pjxysj: "5分钟",
          pf: "9.5分",
        },
      ],
    };
  },
  mounted() {
    this.initPieChart(this.pieData);
    this.initLineChart(this.lineData);
  },
  methods: {
    initPieChart(data) {
      let total = 0;
      data.forEach((x) => {
        total += parseFloat(x.value);
      });
      let chart = echarts.init(document.getElementById("piechart"));
      let option = {
        color: ["#0E42D2", "#249EFF", "#21CCFF", "#64EADC", "#86DF6C"],
        tooltip: {
          trigger: "item",
        },
        legend: {
          right: "16%",
          top: "center",
          icon: "circle",
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 18,
          orient: "vertical",
          formatter: (name) => {
            let value = parseFloat(data.find((x) => x.name == name).value);
            let rate = ((value / total) * 100).toFixed(2);
            return `{a|${name}}{a|${value}}{b|${rate}%}`;
          },
          textStyle: {
            padding: [0, 0, 0, 6],
            rich: {
              a: {
                width: 60,
              },
            },
          },
        },
        series: [
          {
            type: "pie",
            data: data,
            center: ["24%", "55%"],
            radius: ["60%", "90%"],
            itemStyle: {
              borderWidth: 2,
              borderColor: "#fff",
            },
            label: {
              show: false,
              position: "outside", // 将标签放置在扇区外部
              formatter: "{d}%", // 标签内容格式化
            },
            labelLine: {
              show: false,
              length: 10, // 连接线长度
              length2: 10, // 引导线长度
            },
            data: data,
          },
        ],
      };
      chart.setOption(option);
    },
    initLineChart(data) {
      let chart = echarts.init(document.getElementById("linechart"));
      let option = {
        color: ["#3AA1FF", "#36CB6D"],
        tooltip: {
          trigger: "axis",
        },
        grid: {
          left: "2%",
          right: 0,
          bottom: 0,
          top: "28%",
          containLabel: true,
        },
        legend: {
          icon: "square",
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 20,
          right: 0,
        },
        xAxis: {
          type: "category",
          data: data.map((x) => x.name),
          boundaryGap: true,
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#858D9D",
          },
          axisLine: {
            lineStyle: {
              color: "#DEE3E9",
            },
          },
        },
        yAxis: {
          type: "value",
          name: "单位：个",
          splitNumber: 4,
          axisLabel: {
            color: "#667085",
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: "#DEE3E9",
              type: "dotted",
            },
          },
        },
        series: [
          {
            name: "响应率",
            type: "line",
            showSymbol: false,
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#3AA1FF",
                },
                {
                  offset: 1,
                  color: " #3AA1FF20",
                },
              ]),
            },
            emphasis: {
              focus: "series",
            },
            data: data.map((x) => x.value1),
          },
          {
            name: "解决率",
            type: "line",
            showSymbol: false,
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#36CB6D",
                },
                {
                  offset: 1,
                  color: "#36CB6D20",
                },
              ]),
            },
            emphasis: {
              focus: "series",
            },
            data: data.map((x) => x.value2),
          },
        ],
      };
      chart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-bottom: 10px;
    padding-left: 18px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 2px;
      left: 0;
      width: 10px;
      height: 20px;
      background: url("~@/assets/images/cardTitle_icon.png");
      background-size: 100% 100%;
    }
  }
  .subtitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    padding-left: 10px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 6px;
      width: 3px;
      height: 13px;
      background-color: #0057fe;
      border-radius: 3px;
    }
  }
}
.left,
.right,
.center {
  flex: 1;
}
.left,
.right {
  margin-right: 24px;
}
.left {
  .item {
    width: 100%;
    padding: 18px 20px;
    box-sizing: border-box;
    border-radius: 10px;
    margin-bottom: 10px;
    .name {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 700;
      font-size: 18px;
      color: #1d2129;
      line-height: 28px;
      text-align: left;
      margin-bottom: 9px;
    }
    .text {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 16px;
      color: #4e5969;
      line-height: 28px;
      text-align: left;
    }
    .num {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 700;
      font-size: 28px;
      text-align: left;
      line-height: 42px;
      width: fit-content;
    }
    &:nth-child(1) {
      background: linear-gradient(180deg, #f2f9fe 0%, #e6f4fe 100%);
      .num {
        color: #3ba1ff;
      }
    }
    &:nth-child(2) {
      background: linear-gradient(180deg, #f5fef2 0%, #e6feee 100%);
      .num {
        color: #36cb6d;
      }
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
}
.item_blue {
  background: linear-gradient(180deg, #f2f9fe 0%, #e6f4fe 100%);
}
.item_purple {
  background: linear-gradient(180deg, #f6f7ff 0%, #ececff 100%);
}
.item_green {
  background: linear-gradient(180deg, #f5fef2 0%, #e6feee 100%);
}
</style>
