<template>
  <div class="container">
    <div class="card">
      <div class="cardTitle">资产总览</div>
      <zczl :list="zczlData"></zczl>
    </div>
    <div class="flex-s">
      <div class="card" style="width: 33%; margin-right: 12px">
        <div class="cardTitle">云资源使用量TOP5</div>
        <yzysyqk></yzysyqk>
      </div>
      <div class="card" style="width: 67%">
        <div class="cardTitle">云资源使用情况统计</div>
        <top5 :data1="top5PieData" :data2="top5LineData"></top5>
      </div>
    </div>
    <div class="flex-s">
      <div class="card" style="width: 33%; margin-right: 12px">
        <div class="cardTitle">系统等保级别统计</div>
        <dbjb :data="dbjbData"></dbjb>
      </div>
      <div class="card" style="width: 33%; margin-right: 12px">
        <div class="cardTitle">运维应用所属单位分布</div>
        <zcbq :data="zcbqData"></zcbq>
      </div>
      <div class="card" style="width: 33%">
        <div class="cardTitle">资产数量变化趋势</div>
        <zcsl :data="zcslData"></zcsl>
      </div>
    </div>
  </div>
</template>

<script>
import zczl from "@/views/property/assetsOverview/components/zczl.vue";
import yzysyqk from "@/views/property/assetsOverview/components/yzysyqk.vue";
import top5 from "@/views/property/assetsOverview/components/top5.vue";
import dbjb from "@/views/property/assetsOverview/components/dbjb.vue";
import zcbq from "@/views/property/assetsOverview/components/zcbq.vue";
import zcsl from "@/views/property/assetsOverview/components/zcsl.vue";
import {
  getZczl,
  getXtdbjbtj,
  getYyssdwfb,
  getZcslbhqs,
} from "@/api/property/index";
export default {
  components: { zczl, yzysyqk, dbjb, top5, zcbq, zcsl },
  data() {
    return {
      zczlData: [
        {
          name: "运维单位",
          num: 0,
          unit: "家",
          icon: require("@/assets/images/property/dwzs.png"),
          list: [
            // { label: "市本级单位", value: "0", unit: "家" },
            // { label: "县（市、区）单位", value: "0", unit: "家" },
          ],
        },
        {
          name: "运维应用",
          num: 0,
          unit: "个",
          icon: require("@/assets/images/property/xtzs.png"),
          list: [
            // { label: "运行正常应用", value: "0", unit: "个" },
            // { label: "运行异常应用", value: "0", unit: "个" },
          ],
        },
        {
          name: "入驻供应商",
          num: 0,
          unit: "家",
          icon: require("@/assets/images/property/yzyzs.png"),
          list: [],
        },
        {
          name: "运维人员",
          num: 0,
          unit: "个",
          icon: require("@/assets/images/property/ipzczs.png"),
          list: [
            // { label: "一线运维人员", value: "0", unit: "个" },
            // { label: "二线运维人员", value: "0", unit: "个" },
            // { label: "三线运维人员", value: "0", unit: "个" },
          ],
        },
      ],
      dbjbData: [],
      top5PieData: [
        { name: "极低负载", value: 446 },
        { name: "低负载", value: 665 },
        { name: "正常负载", value: 1480 },
        { name: "高负载", value: 426 },
        { name: "极高负载", value: 114 },
      ],
      top5LineData: [
        { name: "5.18", value1: "4", value2: "30" },
        { name: "5.20", value1: "6", value2: "35" },
        { name: "5.22", value1: "14", value2: "20" },
        { name: "5.24", value1: "4", value2: "40" },
        { name: "5.26", value1: "11", value2: "21" },
        { name: "5.28", value1: "1", value2: "22" },
      ],
      zcslData: [],
      zcbqData: [],
    };
  },
  mounted() {
    this.initData();
  },
  methods: {
    initData() {
      //资产总览
      getZczl().then((res) => {
        //运维单位
        this.zczlData[0].num = res.data.ywdw;
        // this.topList[0].list[0].value = 1;
        // this.topList[0].list[1].value = 1;

        //运维应用
        this.zczlData[1].num = res.data.ywyy;
        // this.zczlData[1].list[0].value = res.data.zcyy;
        // this.zczlData[1].list[1].value = res.data.ycyy;

        //入驻供应商
        this.zczlData[2].num = res.data.rzgys;

        //运维人员
        this.zczlData[3].num = res.data.ywrysl;
        // this.zczlData[3].list[0].value = res.data.yxry;
        // this.zczlData[3].list[1].value = res.data.exry;
        // this.zczlData[3].list[2].value = res.data.sxry;
      });

      //系统等保级别统计
      getXtdbjbtj().then((res) => {
        this.dbjbData = res.data;
      });

      //应用所属单位分布
      getYyssdwfb().then((res) => {
        this.zcbqData = res.data;
      });

      //资产数量变化趋势
      getZcslbhqs().then((res) => {
        this.zcslData = res.data;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 40px;
}
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-right: 30px;
    margin-bottom: 10px;
  }
}
.flex-s {
  display: flex;
  align-items: stretch;
}
</style>
