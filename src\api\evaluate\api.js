import request from '@/utils/request'

// 评价列表
export function evaluateList(query) {
  return request({
    url: '/tyywpt/evaluate/list',
    method: 'get',
    params: query
  })
}

// 评价图表数据
export function detailStatistics(query) {
  return request({
    url: '/tyywpt/evaluate/detailStatistics',
    method: 'get',
    params: query
  })
}

// 头部统计数据
export function headerStatistics(query) {
  return request({
    url: '/tyywpt/evaluate/statistics',
    method: 'get',
    params: query
  })
}

// 修改评价
export function evaluateEdit(data) {
  return request({
    url: '/tyywpt/evaluate/edit',
    method: 'put',
    data: data
  })
}

