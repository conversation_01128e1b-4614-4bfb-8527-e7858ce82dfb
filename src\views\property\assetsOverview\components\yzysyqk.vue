<template>
  <div>
    <div class="tabList flex-c">
      <div
        class="tab"
        v-for="(item, i) in tabList"
        :key="i"
        :class="tabIndex == i ? 'tab_active' : ''"
        @click="changeTab(i)"
      >
        {{ item }}
      </div>
    </div>
    <el-table :data="datalist" style="margin-top: 12px" height="220">
      <el-table-column prop="" label="排行" align="center">
        <template slot-scope="scope">
          <div>{{ scope.$index + 1 }}</div>
        </template> 
      </el-table-column>
      <el-table-column prop="name" label="部门名称" align="center" />
      <el-table-column prop="num" label="数量" align="center" />
    </el-table>
  </div>
</template>

<script>
import { getYzysylTop5 } from "@/api/property/index";
export default {
  data() {
    return {
      tabList: ["ecs", "rds", "slb", "oss"],
      tabIndex: 0,
      datalist: [],
    };
  },
  mounted() {
    this.initData();
  },
  methods: {
    changeTab(i) {
      this.tabIndex = i;
      this.initData();
    },
    initData() {
      getYzysylTop5({ sjType: this.tabList[this.tabIndex] }).then((res) => {
        this.datalist = res.data;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.tabList {
  .tab {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #86909c;
    line-height: 22px;
    text-align: left;
    margin-right: 46px;
    cursor: pointer;
  }
  .tab_active {
    color: #0057fe;
  }
}
</style>
