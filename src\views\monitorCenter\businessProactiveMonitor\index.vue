<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2024-11-25 16:25:55
 * @LastEditors: wjb
 * @LastEditTime: 2025-08-06 16:00:09
-->
<template>
  <div v-loading="loading" :style="'height:' + height">
    <iframe
      :src="src"
      frameborder="no"
      style="width: 100%; height: 100%"
      scrolling="auto"
    />
  </div>
</template>

<script>
import { getDddlUrl } from "@/api/login";

export default {
  name: "businessProactiveMonitor",
  data() {
    return {
      src: "",
      height: document.documentElement.clientHeight - 107 + "px;",
      loading: true,
    };
  },
  computed: {},
  created() {
    const that = this;
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 107 + "px;";
    };
    getDddlUrl({ name: "移动", mk: "业务主动监测" }).then((res) => {
      if (res.code == 200) {
        this.loading = false;
        this.src = res.msg;
      }
    });
  },
  methods: {},
  watch: {},
};
</script>

<style scoped>
</style>
