<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-08-22 16:27:23
 * @LastEditors: wjb
 * @LastEditTime: 2025-08-22 16:29:53
-->
<template>
  <div>
    <el-table :data="displayData">
      <template v-for="(item, i) in keyLabelList">
        <el-table-column
          :key="i"
          :prop="item.key"
          :label="item.label"
          align="center"
          :min-width="item.width"
          :show-overflow-tooltip="item.showTooltip"
        >
          <template slot-scope="scope">
            {{ scope.row[item.key] }}
          </template>
        </el-table-column>
      </template>
      <el-table-column
        label="操作"
        align="center"
        width="180"
        v-if="showControl"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="handleDetail(scope.$index, scope.row)">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 查看更多按钮 -->
    <div v-if="showMoreButton" class="more-button-container">
      <el-button
        type="text"
        @click="toggleExpand"
        class="more-button"
      >
        <i :class="isExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
        {{ isExpanded ? '收起' : `查看更多 (${datalist.length - maxDisplayCount}条)` }}
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    datalist: {
      type: Array,
      default: () => [],
    },
    keyLabelList: {
      type: Array,
      default: () => [],
    },
    showControl: {
      type: Boolean,
      default: false,
    },
    // 最大显示条数，默认10条
    maxDisplayCount: {
      type: Number,
      default: 10,
    },
  },
  data() {
    return {
      isExpanded: false, // 是否展开状态
    };
  },
  computed: {
    // 是否显示"查看更多"按钮
    showMoreButton() {
      return this.datalist.length > this.maxDisplayCount;
    },
    // 实际显示的数据
    displayData() {
      if (!this.showMoreButton || this.isExpanded) {
        return this.datalist;
      }
      return this.datalist.slice(0, this.maxDisplayCount);
    },
  },
  methods: {
    handleDetail() {},
    itemClick(item) {
      this.$emit("itemClick", item);
    },
    // 切换展开/收起状态
    toggleExpand() {
      this.isExpanded = !this.isExpanded;
    },
  },
};
</script>

<style lang="scss" scoped>
.color_red {
  color: red;
}

.more-button-container {
  text-align: center;
  padding: 16px 0;
  border-top: 1px solid #ebeef5;
  margin-top: 8px;

  .more-button {
    color: #409eff;
    font-size: 14px;

    &:hover {
      color: #66b1ff;
    }

    i {
      margin-right: 4px;
    }
  }
}
</style>
