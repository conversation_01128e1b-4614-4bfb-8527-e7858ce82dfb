import request from "@/utils/request";

// 查询工单列表
export function listGd(query) {
  return request({
    url: "/tyywpt/tTyywGd/list",
    method: "get",
    params: query,
  });
}

// 查询项目工单列表
export function listProjectGd(query) {
  return request({
    url: "/tyywpt/tTyywGd/projectlist",
    method: "get",
    params: query,
  });
}

// 获取所有应用列表
export function listAllYy() {
  return request({
    url: "/tyywpt/tTyywYy/listAll",
    method: "get",
  });
}

// 获取工单详细信息
export function getGdInfo(query) {
  return request({
    url: "/tyywpt/tTyywGd/getInfo",
    method: "get",
    params: query,
  });
}

// 新增工单
export function addGd(data) {
  return request({
    url: "/tyywpt/tTyywGd/add",
    method: "post",
    data: data,
  });
}

// 工单处理
export function handleGd(data) {
  return request({
    url: "/tyywpt/tTyywGd/handle",
    method: "post",
    data: data,
  });
}

// 处理人员
export function listClry(query) {
  return request({
    url: "/tyywpt/tTyywYyry/clry",
    method: "get",
    params: query,
  });
}

// 查询工单处理记录列表
export function listGdLog(query) {
  return request({
    url: "/tyywpt/tTyywGdLog/list",
    method: "get",
    params: query,
  });
}

// 查询部门下拉树结构
export function deptTreeSelect(query) {
  return request({
    url: "/system/user/deptTreeNew",
    method: "get",
    params: query,
  });
}

// 获取工单按钮是否可操作
export function getAnzy(data) {
  return request({
    url: "/tyywpt/tTyywGd/anzy",
    method: "post",
    data: data,
  });
}

// 获取抄送人员列表
export function getUsercsList(query) {
  return request({
    url: "/system/user/csList",
    method: "get",
    params: query,
  });
}

// 查询统一运维项目列表-全部
export function listProjectAll(query) {
  return request({
    url: "/tyywpt/tTyywProject/listAll",
    method: "get",
    params: query,
  });
}