<template>
  <el-menu
    :default-active="activeMenu"
    mode="horizontal"
    class="topmenu-container"
    @select="handleSelect"
    @open="handleSubmenuOpen"
  >
    <template v-for="(item, index) in processedTopMenus">
      <el-submenu
        :style="{ '--theme': theme }"
        :index="item.path"
        :key="index"
        :data-path="item.path"
        :data-http="isHttp(item.path)"
        :class="{ 'menu-grayed': item.grayed }"
        popper-class="topmenu-container-two"
        v-if="item.children && item.children.length > 0"
      >
        <template slot="title"
          ><svg-icon
            v-if="item.meta && item.meta.icon && item.meta.icon !== '#'"
            :icon-class="item.meta.icon"
          />
          {{ item.meta.title }}</template
        >
        <el-menu-item
          v-for="(child, cidx) in item.children"
          :index="child.path"
          :key="cidx"
          :data-http="isHttp(child.path)"
          :class="{ 'menu-grayed': child.grayed }"
          >{{ child.meta.title }}</el-menu-item
        >
      </el-submenu>
      <el-menu-item
        v-else
        :style="{ '--theme': theme }"
        :index="item.path"
        :key="index"
        :data-http="isHttp(item.path)"
        :class="{ 'menu-grayed': item.grayed }"
      >
        <template slot="title"
          ><svg-icon
            v-if="item.meta && item.meta.icon && item.meta.icon !== '#'"
            :icon-class="item.meta.icon"
          />
          {{ item.meta.title }}</template
        >
      </el-menu-item>
    </template>
    <!-- 顶部菜单超出数量折叠 -->
    <!-- <el-submenu
      :style="{ '--theme': theme }"
      index="more"
      :key="visibleNumber"
      v-if="topMenus.length > visibleNumber"
    >
      <template slot="title">更多菜单</template>
      <template v-for="(item, index) in topMenus">
        <el-menu-item
          :index="item.path"
          :key="index"
          v-if="index >= visibleNumber"
        >
          <svg-icon
            v-if="item.meta && item.meta.icon && item.meta.icon !== '#'"
            :icon-class="item.meta.icon"
          />
          {{ item.meta.title }}
        </el-menu-item>
      </template>
    </el-submenu> -->
  </el-menu>
</template>

<script>
import { constantRoutes } from "@/router";
import { isHttp } from "@/utils/validate";

// 隐藏侧边栏路由
const hideList = ["/index", "/user/profile"];

export default {
  props: {
    // 需要置灰显示的菜单名称列表
    grayedMenuNames: {
      type: Array,
      default: () => [], // 默认置灰的菜单，可根据实际需求修改
    },
  },
  data() {
    return {
      // 顶部栏初始数
      visibleNumber: 5,
      // 当前激活菜单的 index
      currentIndex: undefined,
    };
  },
  watch: {
    // 监听路由变化，确保菜单高亮状态正确更新
    $route: {
      handler() {
        // 当路由变化时，强制更新菜单激活状态
        this.$nextTick(() => {
          const activePath = this.activeMenu;
          console.log("路由变化，更新菜单激活状态:", activePath);
          // 手动更新菜单激活状态的视觉效果
          this.updateMenuActiveState();
          // 强制更新Element UI菜单组件的激活状态
          this.forceUpdateMenuActive();
        });
      },
      immediate: true,
    },
  },
  computed: {
    theme() {
      return this.$store.state.settings.theme;
    },
    // 顶部显示菜单
    topMenus() {
      let topMenus = [];
      this.routers.map((menu) => {
        if (menu.hidden !== true) {
          // 兼容顶部栏一级菜单内部跳转
          if (menu.path === "/" && menu.children) {
            topMenus.push(menu.children[0]);
          } else {
            if (menu.children) {
              menu.children = menu.children.filter((item) => !item.hidden);
            }
            topMenus.push(menu);
          }
        }
      });
      console.log("topMenus", topMenus);
      return topMenus;
    },
    // 所有的路由信息
    routers() {
      return this.$store.state.permission.topbarRouters;
    },
    // 设置子路由
    childrenMenus() {
      var childrenMenus = [];
      this.routers.map((router) => {
        for (var item in router.children) {
          if (router.children[item].parentPath === undefined) {
            if (router.path === "/") {
              router.children[item].path = "/" + router.children[item].path;
            } else {
              if (!isHttp(router.children[item].path)) {
                router.children[item].path =
                  router.path + "/" + router.children[item].path;
              }
            }
            router.children[item].parentPath = router.path;
          }
          childrenMenus.push(router.children[item]);
        }
      });
      return constantRoutes.concat(childrenMenus);
    },
    // 默认激活的菜单
    activeMenu() {
      const path = this.$route.path;
      let activePath = path;

      // 检查当前路径是否为HTTP路径，如果是则不激活任何菜单
      if (isHttp(path)) {
        return "";
      }

      // 首先检查当前路径是否直接匹配顶部菜单（排除HTTP路径）
      const directMatch = this.topMenus.find(
        (menu) => menu.path === path && !isHttp(menu.path)
      );
      if (directMatch) {
        activePath = path;
        this.$store.dispatch(
          "app/toggleSideBarHide",
          directMatch.children && directMatch.children.length > 0 ? false : true
        );
      } else {
        // 查找当前路径属于哪个顶部菜单的子菜单（排除HTTP路径）
        const parentMenu = this.topMenus.find((menu) => {
          if (menu.children && menu.children.length > 0 && !isHttp(menu.path)) {
            return menu.children.some(
              (child) => child.path === path && !isHttp(child.path)
            );
          }
          return false;
        });

        if (parentMenu) {
          // 如果找到父级菜单，激活父级菜单
          activePath = parentMenu.path;
          this.$store.dispatch("app/toggleSideBarHide", false);
        } else {
          // 处理多级路径，提取顶级菜单路径
          if (
            path !== undefined &&
            path.lastIndexOf("/") > 0 &&
            hideList.indexOf(path) === -1
          ) {
            const tmpPath = path.substring(1, path.length);
            if (!this.$route.meta.link) {
              activePath = "/" + tmpPath.substring(0, tmpPath.indexOf("/"));
              this.$store.dispatch("app/toggleSideBarHide", false);
            }
          } else if (!this.$route.children) {
            activePath = path;
            this.$store.dispatch("app/toggleSideBarHide", true);
          }
        }
      }

      this.activeRoutes(activePath);
      console.log("activeMenu", activePath);
      return activePath;
    },
    // 处理后的顶部菜单（包含置灰状态）
    processedTopMenus() {
      return this.topMenus.map((menu) => {
        const processedMenu = { ...menu };

        // 检查主菜单是否需要置灰
        const isMainMenuGrayed = this.isMenuGrayed(menu);

        // 处理子菜单
        if (menu.children && menu.children.length > 0) {
          processedMenu.children = menu.children.map((child) => ({
            ...child,
            grayed: this.isMenuGrayed(child),
          }));

          // 如果所有子菜单都被置灰，则主菜单也置灰
          const allChildrenGrayed = processedMenu.children.every(
            (child) => child.grayed
          );
          processedMenu.grayed = isMainMenuGrayed || allChildrenGrayed;
        } else {
          processedMenu.grayed = isMainMenuGrayed;
        }

        return processedMenu;
      });
    },
  },
  beforeMount() {
    window.addEventListener("resize", this.setVisibleNumber);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.setVisibleNumber);
  },
  mounted() {
    this.setVisibleNumber();
    // 初始化时更新菜单激活状态
    this.$nextTick(() => {
      this.updateMenuActiveState();
    });
  },
  methods: {
    // 检查是否为HTTP路径（模板中使用）
    isHttp(url) {
      return isHttp(url);
    },
    // 检查菜单是否应该被置灰
    isMenuGrayed(menu) {
      if (!menu || !menu.meta) {
        return false;
      }

      // 根据菜单名称判断是否需要置灰
      const menuTitle = menu.meta.title || menu.name || "";
      return this.grayedMenuNames.includes(menuTitle);
    },
    // 根据路径查找菜单项
    findMenuByPath(path) {
      for (const menu of this.processedTopMenus) {
        if (menu.path === path) {
          return menu;
        }
        if (menu.children && menu.children.length > 0) {
          const childMenu = menu.children.find((child) => child.path === path);
          if (childMenu) {
            return childMenu;
          }
        }
      }
      return null;
    },
    // 动态更新置灰菜单列表
    updateGrayedMenus(menuNames) {
      // 触发组件重新渲染
      this.$forceUpdate();
      console.log("更新置灰菜单列表:", menuNames);
    },
    // 检查菜单是否被置灰
    isMenuGrayedByName(menuName) {
      return this.grayedMenuNames.includes(menuName);
    },
    // 根据宽度计算设置显示栏数
    setVisibleNumber() {
      const width = document.body.getBoundingClientRect().width / 3;
      this.visibleNumber = parseInt(width / 85);
    },
    // 菜单选择事件
    handleSelect(key) {
      // 检查是否是HTTP路径
      if (isHttp(key)) {
        // http(s):// 路径新窗口打开，不更新currentIndex，避免菜单高亮
        window.open(key, "_blank");
        // 阻止菜单激活状态更新，保持当前激活状态
        return;
      }

      this.currentIndex = key;

      // 立即更新菜单激活状态，确保二级菜单能够正确激活
      this.$nextTick(() => {
        this.updateMenuActiveState();
        this.forceUpdateMenuActive();
      });
      const route = this.routers.find((item) => item.path === key);

      // 检查是否是子菜单项
      const isChildMenu = this.topMenus.some(
        (menu) =>
          menu.children && menu.children.some((child) => child.path === key)
      );

      if (!route || !route.children || isChildMenu) {
        // 子菜单或没有子路由的菜单，直接跳转
        const routeMenu = this.childrenMenus.find((item) => item.path === key);
        if (routeMenu && routeMenu.query) {
          let query = JSON.parse(routeMenu.query);
          this.$router.push({ path: key, query: query });
        } else {
          this.$router.push({ path: key });
        }

        // 如果是子菜单，需要找到父级菜单并激活侧边栏
        if (isChildMenu) {
          const parentMenu = this.topMenus.find(
            (menu) =>
              menu.children && menu.children.some((child) => child.path === key)
          );
          if (parentMenu) {
            this.activeRoutes(parentMenu.path);
            this.$store.dispatch("app/toggleSideBarHide", false);
          }
        } else {
          this.$store.dispatch("app/toggleSideBarHide", true);
        }
      } else {
        // 显示左侧联动菜单
        this.activeRoutes(key);
        this.$store.dispatch("app/toggleSideBarHide", false);
      }
    },
    // 当前激活的路由
    activeRoutes(key) {
      var routes = [];
      if (this.childrenMenus && this.childrenMenus.length > 0) {
        this.childrenMenus.map((item) => {
          if (key == item.parentPath || (key == "index" && "" == item.path)) {
            routes.push(item);
          }
        });
      }
      if (routes.length > 0) {
        this.$store.commit("SET_SIDEBAR_ROUTERS", routes);
      } else {
        this.$store.dispatch("app/toggleSideBarHide", true);
      }
    },
    // 手动更新菜单激活状态的视觉效果
    updateMenuActiveState() {
      this.$nextTick(() => {
        const currentPath = this.$route.path;

        // 如果当前路径是HTTP路径，不进行任何激活操作
        if (isHttp(currentPath)) {
          // 移除所有手动添加的激活状态
          const submenuTitles = document.querySelectorAll(
            ".topmenu-container .el-submenu__title"
          );
          submenuTitles.forEach((title) => {
            title.classList.remove("manual-active");
          });
          return;
        }

        // 查找当前路径属于哪个顶部菜单的子菜单（排除HTTP路径）
        const parentMenu = this.topMenus.find((menu) => {
          if (menu.children && menu.children.length > 0 && !isHttp(menu.path)) {
            return menu.children.some(
              (child) => child.path === currentPath && !isHttp(child.path)
            );
          }
          return false;
        });

        // 移除所有手动添加的激活状态
        const submenuTitles = document.querySelectorAll(
          ".topmenu-container .el-submenu__title"
        );
        submenuTitles.forEach((title) => {
          title.classList.remove("manual-active");
        });

        // 移除所有二级菜单的激活状态
        const submenuItems = document.querySelectorAll(
          ".topmenu-container-two .el-menu-item"
        );
        submenuItems.forEach((item) => {
          item.classList.remove("is-active");
        });

        // 如果当前路径是子菜单，为父级菜单添加激活状态
        if (parentMenu && !isHttp(parentMenu.path)) {
          const submenuElement = document.querySelector(
            `.topmenu-container .el-submenu[data-path="${parentMenu.path}"] .el-submenu__title`
          );
          if (submenuElement) {
            submenuElement.classList.add("manual-active");
          }

          // 为当前二级菜单添加激活状态
          setTimeout(() => {
            const activeSubmenuItem = document.querySelector(
              `.topmenu-container-two .el-menu-item[tabindex="${currentPath}"]`
            );
            if (activeSubmenuItem) {
              activeSubmenuItem.classList.add("is-active");
            }
          }, 100);
        }
      });
    },
    // 强制更新Element UI菜单的激活状态
    forceUpdateMenuActive() {
      this.$nextTick(() => {
        const currentPath = this.$route.path;

        // 查找Element UI菜单组件实例
        const menuComponent = this.$children.find(child => child.$options.name === 'ElMenu');
        if (menuComponent) {
          // 强制设置激活菜单项
          menuComponent.activeIndex = currentPath;
          menuComponent.$forceUpdate();
        }

        // 直接操作DOM来确保二级菜单激活状态
        setTimeout(() => {
          // 移除所有二级菜单的激活状态
          const allSubmenuItems = document.querySelectorAll('.topmenu-container-two .el-menu-item');
          allSubmenuItems.forEach(item => {
            item.classList.remove('is-active');
          });

          // 为当前路径对应的二级菜单添加激活状态
          const currentSubmenuItem = Array.from(allSubmenuItems).find(item => {
            return item.getAttribute('tabindex') === currentPath ||
                   item.textContent.trim() === this.getCurrentMenuTitle(currentPath);
          });

          if (currentSubmenuItem) {
            currentSubmenuItem.classList.add('is-active');
          }
        }, 50);
      });
    },
    // 获取当前菜单标题
    getCurrentMenuTitle(path) {
      for (const menu of this.topMenus) {
        if (menu.children && menu.children.length > 0) {
          const child = menu.children.find(child => child.path === path);
          if (child && child.meta) {
            return child.meta.title;
          }
        }
      }
      return '';
    },
    // 处理子菜单打开事件
    handleSubmenuOpen(index) {
      // 如果打开的是HTTP路径的子菜单，不进行任何激活操作
      if (isHttp(index)) {
        return;
      }
    },
  },
};
</script>

<style lang="scss">
.topmenu-container.el-menu--horizontal > .el-menu-item {
  float: left;
  height: 50px !important;
  line-height: 50px !important;
  color: #1d2129 !important;
  padding: 0 5px !important;
  margin: 0 10px !important;
}

.topmenu-container.el-menu--horizontal > .el-menu-item.is-active,
.topmenu-container.el-menu--horizontal
  > .el-submenu.is-active
  .el-submenu__title {
  border-bottom: 2px solid #{"var(--theme)"} !important;
  color: #409eff !important;
}

/* 确保子菜单选中时父级菜单也显示激活状态 */
.topmenu-container.el-menu--horizontal
  > .el-submenu:has(.el-menu-item.is-active)
  .el-submenu__title,
.topmenu-container.el-menu--horizontal
  > .el-submenu
  .el-submenu__title.manual-active {
  border-bottom: 2px solid #{"var(--theme)"} !important;
  color: #409eff !important;
}

/* 防止HTTP路径菜单项显示激活状态 */
.topmenu-container.el-menu--horizontal
  > .el-menu-item[data-http="true"].is-active,
.topmenu-container.el-menu--horizontal
  > .el-submenu[data-http="true"]
  .el-submenu__title.is-active {
  border-bottom: none !important;
  color: #1d2129 !important;
}

/* 置灰菜单样式 */
.topmenu-container.el-menu--horizontal > .el-menu-item.menu-grayed,
.topmenu-container.el-menu--horizontal
  > .el-submenu.menu-grayed
  .el-submenu__title {
  color: #c0c4cc !important;
  opacity: 0.6;

  &:hover {
    color: #c0c4cc !important;
  }

  .svg-icon {
    color: #c0c4cc !important;
  }
}

/* 置灰的子菜单项样式 */
.topmenu-container .el-menu-item.menu-grayed {
  color: #c0c4cc !important;
  opacity: 0.6;

  &:hover {
    color: #c0c4cc !important;
  }
}

/* 当主菜单被置灰时，整个子菜单容器也置灰 */
.topmenu-container.el-menu--horizontal > .el-submenu.menu-grayed {
  .el-submenu__title {
    color: #c0c4cc !important;
    opacity: 0.6;

    &:hover {
      color: #c0c4cc !important;
    }
  }

  .el-menu-item {
    color: #c0c4cc !important;
    opacity: 0.6;

    &:hover {
      color: #c0c4cc !important;
    }
  }
}

/* submenu item */
.topmenu-container.el-menu--horizontal > .el-submenu .el-submenu__title {
  float: left;
  height: 50px !important;
  line-height: 50px !important;
  color: #1d2129 !important;
  padding: 0 5px !important;
  margin: 0 10px !important;
}
/* 二级菜单样式 */
.topmenu-container-two.el-menu--horizontal .el-menu {
  .el-menu-item {
    color: #1d2129 !important; /* 默认颜色 */

    &:hover {
      color: #409eff !important; /* 悬浮颜色 */
      background-color: #ecf5ff !important;
    }

    &.is-active {
      color: #409eff !important; /* 激活颜色 */
      background-color: #ecf5ff !important;
    }

    /* 二级菜单置灰样式 */
    &.menu-grayed {
      color: #c0c4cc !important;
      opacity: 0.6;

      &:hover {
        color: #c0c4cc !important; /* 置灰时hover不变色 */
        background-color: transparent !important;
      }

      &.is-active {
        color: #c0c4cc !important; /* 置灰时active不变色 */
        background-color: transparent !important;
      }
    }
  }
}

.el-submenu__icon-arrow {
  visibility: hidden;
}
</style>
