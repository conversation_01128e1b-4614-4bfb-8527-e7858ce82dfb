<template>
  <div style="width: 100%; height: 223px" id="gjdwpm"></div>
</template>

<script>
import * as echarts from "echarts";
export default {
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  mounted() {},
  watch: {
    data: {
      handler(val) {
        this.initBarChart(val, "gjdwpm");
      },
      deep: true,
    },
  },
  methods: {
    initBarChart(data, id) {
      const barChart = echarts.init(document.getElementById(id));
      const option = {
        color: ["#86DF6C"],
        grid: {
          left: "2%",
          right: "5%",
          top: "16%",
          bottom: "0%",
          containLabel: true,
        },
        tooltip: {
          trigger: "item",
        },
        xAxis: {
          type: "category",
          data: data.map((x) => x.name),
          boundaryGap: true,
          axisTick: {
            show: false,
          },
          axisLabel: {
            interval: 0,
            color: "#858D9D",
            width: 50,
            overflow: "breakAll",
            lineHeight: 16,
          },
          axisLine: {
            lineStyle: {
              color: "#DEE3E9",
            },
          },
        },
        yAxis: {
          type: "value",
          name: "单位：个",
          splitNumber: 4,
          axisLabel: {
            color: "#667085",
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: "#DEE3E9",
              type: "dotted",
            },
          },
        },
        series: [
          {
            type: "bar",
            barWidth: 14,
            label: { show: false },
            data: data.map((v) => v.num),
          },
        ],
      };

      barChart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
