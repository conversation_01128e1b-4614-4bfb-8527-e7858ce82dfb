<template>
  <div class="container">
    <div class="card">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        label-width="100px"
        class="queryForm"
      >
        <el-form-item label="单位名称" prop="dwmc">
          <el-input
            v-model="queryParams.dwmc"
            placeholder="请输入"
            clearable
            style="width: 160px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="所属行业" prop="sshy">
          <el-select
            v-model="queryParams.sshy"
            placeholder="请选择"
            clearable
            style="width: 160px"
          >
            <el-option
              v-for="(item, i) in sshyOptions"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="展示类型" prop="zzlx">
          <el-select
            v-model="queryParams.zzlx"
            placeholder="请选择"
            clearable
            style="width: 160px"
          >
            <el-option
              v-for="(item, i) in zslxOptions"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="单位简称" prop="dwjc">
          <el-input
            v-model="queryParams.dwjc"
            placeholder="请输入"
            clearable
            style="width: 160px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item style="margin-left: 30px">
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">
            查询
          </el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="flex-s">
      <div class="card" style="flex: 1; margin-right: 12px">
        <div class="flex-c">
          <div class="cardTitle">任务概览</div>
          <img
            src="@/assets/images/property/question.png"
            class="question_icon"
          />
        </div>
        <div class="itemBox">
          <div class="item flex-b">
            <div class="name">单位总数：</div>
            <div class="num1">
              {{ dwzs }}
              <span class="unit">个</span>
            </div>
          </div>
        </div>
        <div class="itemBox">
          <div class="item flex-b">
            <div class="name">风险单位：</div>
            <div class="num2">
              {{ fxdw }}
              <span class="unit">个</span>
            </div>
          </div>
        </div>
      </div>
      <div class="card" style="flex: 1; margin-right: 12px">
        <div class="cardTitle">风险单位行业分布</div>
        <div id="barchart" style="width: 100%; height: 200px"></div>
      </div>
      <div class="card" style="flex: 1">
        <div class="cardTitle">单位类型分布</div>
        <div id="piechart" style="width: 100%; height: 200px"></div>
      </div>
    </div>

    <div class="card flex-b" style="padding: 0">
      <div class="tabList flex-c">
        <div
          class="tab"
          v-for="(item, i) in tabList"
          :key="i"
          :class="tabIndex == i ? 'tab_active' : ''"
          @click="changeTab(i)"
        >
          {{ item }}
        </div>
      </div>
      <div class="total">
        已找到<span class="blue"> {{ total }} </span>个单位
      </div>
    </div>

    <div class="card li" v-for="(item, i) in datalist" :key="i">
      <div class="flex-b">
        <div class="flex-c">
          <div class="name">{{ item.name }}</div>
          <div v-if="item.tag && item.tag.length > 0" class="flex-c">
            <div class="tag flex-c-c" v-for="(x, j) in item.tag" :key="j">
              {{ x }}
            </div>
          </div>
        </div>
        <div class="flex-c">
          <img
            v-if="item.isLike"
            src="@/assets/images/property/smile_fill.png"
            class="icon1"
            @click="item.isLike = !item.isLike"
          />
          <img
            v-else
            src="@/assets/images/property/smile.png"
            class="icon1"
            @click="item.isLike = !item.isLike"
          />
          <img
            v-if="item.isCollect"
            src="@/assets/images/property/star_fill.png"
            class="icon2"
            @click="item.isCollect = !item.isCollect"
          />
          <img
            v-else
            src="@/assets/images/property/star.png"
            class="icon2"
            @click="item.isCollect = !item.isCollect"
          />
          <img src="@/assets/images/property/more.png" class="icon3" />
        </div>
      </div>
      <div class="flex-c" style="margin-top: 10px">
        <div class="left">
          <div class="line flex-c">
            <img src="@/assets/images/property/info.png" class="icon1" />
            <div class="value">{{ item.info || "暂无数据" }}</div>
          </div>
          <div class="line flex-c">
            <img src="@/assets/images/property/location.png" class="icon2" />
            <div class="value">{{ item.xxdz }}</div>
          </div>
        </div>
        <div class="right flex-c">
          <div class="item" v-for="(x, j) in item.list" :key="j">
            <div class="name">{{ x.name }}</div>
            <div class="flex-b">
              <img :src="x.icon" class="img" />
              <div style="width: 100%">
                <div class="flex-b">
                  <div class="label">
                    {{ i == 0 ? "已失陷" : i == 1 ? "本月处置" : "安全事件" }}
                  </div>
                  <div class="label">{{ x.value1 }}个</div>
                </div>
                <div class="flex-b">
                  <div class="label">
                    {{ i == 0 ? "全部" : i == 1 ? "本年处置" : "风险隐患" }}
                  </div>
                  <div class="label">{{ x.value2 }}个</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="item.bottom_tag && item.bottom_tag.length > 0" class="flex-c">
        <div class="tag2 flex-c-c" v-for="(x, j) in item.bottom_tag" :key="j">
          {{ x }}
        </div>
      </div>
    </div>
    <div
      class="card"
      style="display: flex; justify-content: flex-end; margin-top: 20px"
    >
      <el-pagination
        @current-change="getList"
        :current-page="queryParams.pageNum"
        :page-size="queryParams.pageNum"
        layout="total, prev, pager, next,jumper"
        :total="total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  data() {
    return {
      queryParams: {
        pageSize: 10,
        pageNum: 1,
        dwmc: "",
        sshy: "",
        zslx: "",
        dwjc: "",
      },
      sshyOptions: [],
      zslxOptions: [],
      //
      dwzs: "1798",
      fxdw: "0",
      barData: [
        { name: "政府", value1: "35", value2: 12 },
        { name: "教育", value1: "26", value2: 8 },
      ],
      pieData: [
        { name: "政府机关", value: 20 },
        { name: "其他", value: 20 },
        { name: "党委机关", value: 20 },
        { name: "大型国企", value: 20 },
        { name: "社会团体", value: 20 },
      ],
      //
      tabList: ["单位卡片", "单位管理"],
      tabIndex: 0,
      total: 1798,
      datalist: [
        {
          name: "武义县",
          tag: ["政府机关", "政府"],
          info: "",
          xxdz: "浙江省金华市武义县",
          bottom_tag: ["Pink家族远控木马活动事件", "远控木马活动事件"],
          list: [
            {
              name: "资产总数",
              value1: 0,
              value2: 4190,
              icon: require("@/assets/images/property/zcds.png"),
            },
            {
              name: "通报预警",
              value1: 0,
              value2: 4190,
              icon: require("@/assets/images/property/tbyj.png"),
            },
            {
              name: "实时监测",
              value1: 0,
              value2: 4190,
              icon: require("@/assets/images/property/ssjc.png"),
            },
          ],
          isLike: false,
          isCollect: false,
        },
        {
          name: "武义县",
          tag: ["政府机关", "政府"],
          info: "",
          xxdz: "浙江省金华市武义县",
          bottom_tag: ["Pink家族远控木马活动事件", "远控木马活动事件"],
          list: [
            {
              name: "资产总数",
              value1: 0,
              value2: 4190,
              icon: require("@/assets/images/property/zcds.png"),
            },
            {
              name: "通报预警",
              value1: 0,
              value2: 4190,
              icon: require("@/assets/images/property/tbyj.png"),
            },
            {
              name: "实时监测",
              value1: 0,
              value2: 4190,
              icon: require("@/assets/images/property/ssjc.png"),
            },
          ],
          isLike: false,
          isCollect: false,
        },
      ],
    };
  },
  mounted() {
    this.getList();
    this.initBarChart(this.barData);
    this.initPieChart(this.pieData);
  },
  methods: {
    changeTab(i) {
      this.tabIndex = i;
    },
    getList() {
      // console.log("Search keyword:", this.searchKeyword);
    },

    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        aqsjmc: "",
        aqsjdj: "",
        shz: "",
      };
    },
    initBarChart(data) {
      let chart = echarts.init(document.getElementById("barchart"));
      let option = {
        color: ["#3AA1FF", "#F98E1B"],
        tooltip: {
          trigger: "axis",
        },
        legend: {
          right: 20,
          top: 0,
          icon: "sqaure",
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 30,
        },
        grid: {
          left: "2%",
          right: 0,
          bottom: 0,
          top: "16%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: data.map((x) => x.name),
          boundaryGap: true,
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#858D9D",
          },
          axisLine: {
            lineStyle: {
              color: "#DEE3E9",
            },
          },
        },
        yAxis: {
          type: "value",
          name: "单位：",
          splitNumber: 4,
          axisLabel: {
            color: "#667085",
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: "#DEE3E9",
              type: "dotted",
            },
          },
        },
        series: [
          {
            name: "全部单位",
            type: "bar",
            barWidth: "8%",
            data: data.map((x) => x.value1),
          },
          {
            name: "风险单位",
            type: "bar",
            barWidth: "8%",
            data: data.map((x) => x.value2),
          },
        ],
      };
      chart.setOption(option);
    },
    initPieChart(data) {
      let total = 0;
      data.forEach((x) => {
        total += parseFloat(x.value);
      });
      let chart = echarts.init(document.getElementById("piechart"));
      let option = {
        color: ["#0E42D2", "#249EFF", "#21CCFF", "#64EADC", "#86DF6C"],
        tooltip: {
          trigger: "item",
        },
        legend: {
          right: "16%",
          top: "center",
          icon: "circle",
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 18,
          orient: "vertical",
          formatter: (name) => {
            let value = (
              (parseFloat(data.find((x) => x.name == name).value) / total) *
              100
            ).toFixed(2);
            return `{a|${name}}{b|${value}%}`;
          },
          textStyle: {
            padding: [0, 0, 0, 6],
            rich: {
              a: {
                width: 80,
              },
            },
          },
        },
        series: [
          {
            type: "pie",
            data: data,
            center: ["24%", "50%"],
            radius: ["56%", "80%"],
            itemStyle: {
              borderWidth: 2,
              borderColor: "#fff",
            },
            label: {
              show: false,
              position: "outside", // 将标签放置在扇区外部
              formatter: "{d}%", // 标签内容格式化
            },
            labelLine: {
              show: false,
              length: 10, // 连接线长度
              length2: 10, // 引导线长度
            },
            data: data,
          },
        ],
      };
      chart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  padding-bottom: 30px;
  .card {
    width: 100%;
    border-radius: 15px;
    padding: 20px 20px;
    box-sizing: border-box;
    background-color: #fff;
    height: auto;
    margin-bottom: 12px;
    .cardTitle {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 700;
      font-size: 18px;
      color: #1d2129;
      line-height: 24px;
      text-align: left;
      margin-bottom: 15px;
    }
  }
  .question_icon {
    margin-bottom: 15px;
    margin-left: 6px;
  }
}
.queryForm {
  ::v-deep .el-form-item {
    margin-bottom: 0;
  }
}
.itemBox {
  width: 100%;
  background: linear-gradient(180deg, #e5e6eb, #e5e6eb00);
  border-radius: 10px;
  padding: 1px;
  box-sizing: border-box;
  .item {
    width: 100%;
    padding: 25px 20px;
    box-sizing: border-box;
    border-radius: 10px;
    background-color: #fff;
    margin-bottom: 12px;
    &:last-child {
      margin-bottom: 0;
    }
    .name {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 14px;
      color: #4e5969;
      line-height: 20px;
    }
    .num1 {
      font-family: Roboto, Roboto;
      font-weight: 600;
      font-size: 30px;
      color: #1d4ed8;
      line-height: 36px;
    }
    .num2 {
      font-family: Roboto, Roboto;
      font-weight: 600;
      font-size: 30px;
      color: #ef4444;
      line-height: 36px;
    }
    .unit {
      font-size: 16px;
    }
  }
}
.tabList {
  padding: 0 20px;
  box-sizing: border-box;
  .tab {
    margin-right: 56px;
    padding: 20px 0;
    box-sizing: border-box;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #1d2129;
    line-height: 22px;
    text-align: left;
    cursor: pointer;
  }
  .tab_active {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 700;
    font-size: 14px;
    color: #1d2129;
    line-height: 22px;
    border-bottom: solid 3px #0057fe;
  }
}
.total {
  padding: 20px 30px;
  box-sizing: border-box;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #1d2129;
  line-height: 22px;
  .blue {
    color: #0057fe;
  }
}
.li {
  .name {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    margin-right: 16px;
  }
  .tag {
    background: #eff6ff;
    border-radius: 4px;
    padding: 2px 8px;
    box-sizing: border-box;
    font-family: Roboto, Roboto;
    font-weight: 400;
    font-size: 12px;
    color: #1d4ed8;
    line-height: 18px;
    margin-right: 8px;
  }
  .icon1,
  .icon2 {
    width: 16px;
    height: 16px;
    margin-right: 24px;
    cursor: pointer;
  }
  .icon3 {
    width: 14px;
    height: auto;
    cursor: pointer;
  }
  .left {
    flex: 0.7;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .line {
      margin: 9px 0;
      .icon1 {
        width: 14px;
        height: 14px;
        margin-right: 8px;
      }
      .icon2 {
        width: 11px;
        height: 15px;
        margin-right: 8px;
      }
      .value {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 14px;
        color: #4e5969;
        line-height: 28px;
        text-align: left;
      }
    }
  }
  .right {
    flex: 3;
    height: 128px;
    .item {
      flex: 1;
      margin-right: 46px;
      padding: 8px 16px;
      box-sizing: border-box;
      background: linear-gradient(
        180deg,
        #e6f4fe 0%,
        rgba(242, 249, 254, 0.32) 100%
      );
      border-radius: 10px 10px 10px 10px;
      .name {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 16px;
        color: #4e5969;
        line-height: 28px;
        text-align: left;
        margin-bottom: 11px;
      }
      .img {
        width: 65px;
        height: 65px;
        margin-right: 25px;
      }
      .label {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 16px;
        color: #4e5969;
        line-height: 28px;
        text-align: left;
      }
      .value {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 16px;
        color: #1d2129;
        line-height: 28px;
        text-align: left;
      }
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .tag2 {
    background: #fee2e2;
    border-radius: 4px;
    padding: 2px 8px;
    box-sizing: border-box;
    font-family: Roboto, Roboto;
    font-weight: 400;
    font-size: 12px;
    color: #dc2626;
    line-height: 18px;
    margin-right: 8px;
  }
}
.flex-s {
  display: flex;
  align-items: stretch;
}
</style>
