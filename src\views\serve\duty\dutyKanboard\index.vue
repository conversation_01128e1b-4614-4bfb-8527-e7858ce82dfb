<template>
  <div>
    <div class="card flex-b" style="width: 100%">
      <div
        class="item"
        v-for="(item, i) in topList"
        :key="i"
        :class="i == 0 ? 'item_blue' : i == 1 ? 'item_purple' : 'item_green'"
      >
        <div class="flex-b">
          <div>
            <div class="name">{{ item.name }}</div>
            <div class="flex-c" style="margin-top: 10px">
              <div class="num">
                {{ item.num }}
                <span class="unit">
                  {{ i == 0 ? "人" : item == 1 ? "个" : "%" }}
                </span>
              </div>
              <div class="name" v-if="i == 0">已签到</div>
              <div class="name" v-if="i == 1">
                其中紧急告警数:{{ item.num2 }}个
              </div>
              <el-button type="text" v-if="i == 1" style="margin-left: 20px">
                查看详情<i class="el-icon-arrow-right el-icon--right" bold></i>
              </el-button>
              <div class="name" v-if="i == 2">
                较昨日下降:
                <span style="color: #00b42a">{{ item.num2 }}%</span>
                <img src="@/assets/images/serve/down_green.png" alt="" />
              </div>
            </div>
          </div>
          <div>
            <img :src="item.icon" class="icon" />
          </div>
        </div>
      </div>
    </div>
    <div class="card">
      <div class="cardTitle">值班人员</div>
      <el-table :data="datalist1">
        <el-table-column prop="name" label="姓名" align="center">
          <template slot-scope="scope">
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              <span
                >{{ formatName(scope.row.name, scope.row.id)
                }}<!-- 显示状态：睁眼图标 --></span
              ><i
                v-if="nameVisibilityMap[scope.row.id]"
                class="el-icon-view"
                style="
                  margin-left: 4px;
                  cursor: pointer;
                  color: #409eff;
                  font-size: 12px;
                "
                @click="toggleNameVisibility(scope.row.id)"
                title="隐藏姓名"
              ></i>
              <i
                v-else
                class="custom-eye-hide"
                style="
                  margin-left: 4px;
                  cursor: pointer;
                  color: #409eff;
                  font-size: 12px;
                "
                @click="toggleNameVisibility(scope.row.id)"
                title="显示姓名"
              ></i>
            </div> </template
        ></el-table-column>
        <el-table-column prop="zbfzr" label="值班负责人" align="center" />
        <el-table-column prop="bm" label="部门" align="center" />
        <el-table-column prop="zc" label="职称" align="center" />
        <el-table-column prop="lxdh" label="联系电话" align="center">
          <template slot-scope="scope">
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              <span @click="doCall(scope.row)"
                >{{ formatPhone(scope.row.lxdh, scope.row.id)
                }}<!-- 显示状态：睁眼图标 --></span
              ><i
                v-if="phoneVisibilityMap[scope.row.id]"
                class="el-icon-view"
                style="margin-left: 8px; cursor: pointer; color: #409eff"
                @click="togglePhoneVisibility(scope.row.id)"
                title="隐藏手机号"
              ></i>
              <!-- 隐藏状态：闭眼图标 -->
              <i
                v-else
                class="custom-eye-hide"
                style="margin-left: 8px; cursor: pointer; color: #409eff"
                @click="togglePhoneVisibility(scope.row.id)"
                title="显示手机号"
              ></i>
            </div> </template
        ></el-table-column>
      </el-table>
    </div>
    <div class="card">
      <div class="cardTitle">待处理告警</div>
      <el-table :data="datalist2">
        <el-table-column prop="gjsj" label="告警时间" align="center" />
        <el-table-column prop="gjjb" label="告警级别" align="center">
          <template slot-scope="scope">
            <span
              :class="
                scope.row.gjjb == '高'
                  ? 'text_red'
                  : scope.row.gjjb == '中'
                  ? 'text_org'
                  : 'text_blue'
              "
              >{{ scope.row.gjjb }}</span
            >
          </template>
        </el-table-column>
        <el-table-column prop="gjnr" label="告警内容" align="center" />
        <el-table-column prop="gjxt" label="告警系统" align="center" />
        <el-table-column prop="clzt" label="处理状态" align="center">
          <template slot-scope="scope">
            <el-button @click="doCall(scope.row)" type="text">
              {{ scope.row.clzt }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      // 手机号码显示状态控制
      phoneVisibilityMap: {},
      // 姓名显示状态控制
      nameVisibilityMap: {},
      topList: [
        {
          name: "当前值班人数",
          num: 3,
          icon: require("@/assets/images/serve/person_persent.png"),
        },
        {
          name: "待处理告警数",
          num: 12,
          num2: 3,
          icon: require("@/assets/images/serve/alarm_num.png"),
        },
        {
          name: "工单响应时效",
          num: 85,
          num2: 5,
          icon: require("@/assets/images/serve/worksheet_time.png"),
        },
      ],
      datalist1: [
        {
          id: 1,
          name: "崔漠桐",
          zbfzr: "是",
          bm: "运维部",
          zc: "工程师",
          lxdh: "18768108880",
        },
        {
          id: 2,
          name: "崔漠桐",
          zbfzr: "是",
          bm: "运维部",
          zc: "工程师",
          lxdh: "18768108880",
        },
        {
          id: 3,
          name: "崔漠桐",
          zbfzr: "是",
          bm: "运维部",
          zc: "工程师",
          lxdh: "18768108880",
        },
      ],
      datalist2: [
        {
          gjsj: "15:30:25",
          gjjb: "高",
          gjnr: "数据库服务器CPU使用率超过90%",
          gjxt: "生产环境",
          clzt: "待处理",
        },
        {
          gjsj: "15:30:25",
          gjjb: "中",
          gjxt: "生产环境",
          gjnr: "数据库服务器CPU使用率超过90%",
          clzt: "待处理",
        },
        {
          gjsj: "15:30:25",
          gjjb: "低",
          gjxt: "生产环境",
          gjnr: "数据库服务器CPU使用率超过90%",
          clzt: "待处理",
        },
      ],
    };
  },
  mounted() {
    // 初始化显示状态（默认隐藏）
    this.initVisibility();
  },
  methods: {
    // 初始化显示状态
    initVisibility() {
      const phoneVisibilityMap = {};
      const nameVisibilityMap = {};
      this.noticeList.forEach((item) => {
        phoneVisibilityMap[item.id] = false; // 默认隐藏手机号
        nameVisibilityMap[item.id] = false; // 默认隐藏姓名
      });
      this.phoneVisibilityMap = phoneVisibilityMap;
      this.nameVisibilityMap = nameVisibilityMap;
    },
    // 格式化姓名显示（脱敏处理）
    formatName(name, id) {
      if (!name) return "";

      // 根据姓名显示状态决定是否脱敏
      if (this.nameVisibilityMap[id]) {
        return name; // 显示完整姓名
      } else {
        // 姓名脱敏处理
        if (name.length === 1) {
          return name; // 单字姓名不脱敏
        } else if (name.length === 2) {
          return name.charAt(0) + "*"; // 两字姓名：张*
        } else if (name.length === 3) {
          return name.charAt(0) + "*" + name.charAt(2); // 三字姓名：张*三
        } else {
          // 四字及以上姓名：保留首尾，中间用*代替
          return (
            name.charAt(0) +
            "*".repeat(name.length - 2) +
            name.charAt(name.length - 1)
          );
        }
      }
    },
    // 格式化手机号码显示
    formatPhone(phone, id) {
      if (!phone) return "";

      // 检查是否为有效的手机号码格式
      if (!/^1[3-9]\d{9}$/.test(phone)) {
        return phone; // 如果不是标准手机号格式，直接返回原值
      }

      // 根据显示状态决定是否隐藏中间四位
      if (this.phoneVisibilityMap[id]) {
        return phone; // 显示完整手机号
      } else {
        return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2"); // 隐藏中间四位
      }
    },
    // 切换姓名显示状态
    toggleNameVisibility(id) {
      this.$set(this.nameVisibilityMap, id, !this.nameVisibilityMap[id]);
    },
    // 切换手机号码显示状态
    togglePhoneVisibility(id) {
      this.$set(this.phoneVisibilityMap, id, !this.phoneVisibilityMap[id]);
    },
    doCall(row) {},
  },
};
</script>

<style lang="scss" scoped>
/* 自定义闭眼图标 */
.custom-eye-hide {
  display: inline-block;
  width: 16px;
  height: 16px;
  position: relative;
  font-style: normal;
  background: url("~@/assets/images/eyes_hide.png") 0 0 no-repeat;
  background-size: cover;
}
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-bottom: 10px;
    padding-left: 18px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 2px;
      left: 0;
      width: 10px;
      height: 20px;
      background: url("~@/assets/images/cardTitle_icon.png");
      background-size: 100% 100%;
    }
  }
}
.item {
  width: 32%;
  height: 159px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border-radius: 10px 10px 10px 10px;
  padding: 0 20px 0 26px;
  .name {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 16px;
    color: #4e5969;
    line-height: 28px;
    text-align: left;
  }
  .num {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 24px;
    color: #1d2129;
    line-height: 42px;
    text-align: left;
    margin-right: 24px;
    .unit {
      font-size: 18px;
    }
  }
  .icon {
    width: 65px;
    height: 65px;
  }
}
.item_blue {
  background: linear-gradient(180deg, #f2f9fe 0%, #e6f4fe 100%);
}
.item_purple {
  background: linear-gradient(180deg, #f6f7ff 0%, #ececff 100%);
}
.item_green {
  background: linear-gradient(180deg, #f5fef2 0%, #e6feee 100%);
}
.text_red {
  color: #e85141;
}
.text_org {
  color: #f98f1c;
}
.text_blue {
  color: #249eff;
}
</style>
