<template>
  <div class="container">
    <div class="timeSelector flex-c">
      <div>统计日期</div>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        size="mini"
      >
      </el-date-picker>
    </div>
    <div class="card">
      <div class="tabList flex-b">
        <div class="tabCon" v-for="(item, i) in tabList" :key="i">
          <div
            class="tab"
            :class="tabIndex == i ? 'tab_active' : ''"
            @click="changeTab(i)"
          >
            <div class="name">{{ item.name }}</div>
            <div class="num">({{ item.num }})</div>
          </div>
          <div class="imgBox flex-c-c" v-if="i !== tabList.length - 1">
            <img src="@/assets/images/warning/arrow-right.png" class="icon" />
          </div>
        </div>
      </div>
    </div>
    <div class="flex-c" style="margin-bottom: 12px">
      <el-button
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleAdd"
        >新增</el-button
      >
      <el-button size="small" @click="handleDelete">删除</el-button>
      <el-button size="small" @click="handleImport">导入</el-button>
      <el-button size="small" @click="handleExport">导出</el-button>
      <el-button size="small" @click="batchInform">批量通知</el-button>
    </div>
    <div class="card">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        label-width="100px"
      >
        <el-form-item label="告知标题" prop="gzbt">
          <el-input
            v-model="queryParams.gzbt"
            placeholder="请输入关键字"
            clearable
            style="width: 160px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="告警级别" prop="gjjb">
          <el-select
            v-model="queryParams.gjjb"
            placeholder="请选择"
            clearable
            style="width: 160px"
          >
            <el-option
              v-for="(item, i) in gjjbOptions"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="告警对象" prop="gjdx">
          <el-select
            v-model="queryParams.gjdx"
            placeholder="请选择"
            clearable
            style="width: 160px"
          >
            <el-option
              v-for="(item, i) in gjdxOptions"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin-left: 30px">
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">
            查询
          </el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="tableData" v-loading="loading">
        <el-table-column prop="ldmc" label="漏洞名称" align="center" />
        <el-table-column prop="ldlx" label="漏洞类型" align="center" />
        <el-table-column
          prop="lddj"
          label="漏洞等级"
          align="center"
          width="80px"
        >
          <template slot-scope="scope">
            <div class="flex-c-c">
              <div
                class="tag flex-c-c"
                :class="
                  scope.row.lddj == '严重'
                    ? 'tag_red'
                    : scope.row.lddj == '中危'
                    ? 'tag_yel'
                    : scope.row.lddj == '高危'
                    ? 'tag_org'
                    : ''
                "
              >
                {{ scope.row.lddj }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="ldip" label="漏洞IP/域名" align="center" />
        <el-table-column prop="lddk" label="漏洞端口" align="center" />
        <el-table-column prop="ldsjfw" label="漏洞涉及的服务" align="center" />
        <el-table-column prop="ldsjyy" label="漏洞涉及的应用" align="center" />
        <el-table-column prop="dwmc" label="单位名称" align="center" />
        <el-table-column prop="qssj" label="起始时间" align="center" />
        <el-table-column prop="gxsj" label="更新时间" align="center" />
        <el-table-column label="操作" align="center" width="180">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="handleDetail(scope.$index, scope.row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div style="display: flex; justify-content: flex-end; margin-top: 20px">
        <el-pagination
          @current-change="getList"
          :current-page="queryParams.pageNum"
          :page-size="queryParams.pageNum"
          layout="total, prev, pager, next"
          :total="total"
        ></el-pagination>
      </div>
    </div>

    <!-- 弹窗 -->
    <infoDialog :show="show" :info="info" @close="show=false"></infoDialog>
  </div>
</template>

<script>
import infoDialog from "@/views/warning/riskList/components/dialog.vue";
export default {
  components: { infoDialog },
  data() {
    return {
      dateRange: [],
      tabList: [
        { name: "全部告知", num: "417" },
        { name: "发起", num: "0" },
        { name: "签收", num: "0" },
        { name: "处置", num: "1" },
        { name: "终审", num: "1" },
        { name: "完成", num: "415" },
      ],
      tabIndex: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        gzbt: undefined,
        gjjb: undefined,
        gjdx: undefined,
      },
      gjjbOptions: [],
      gjdxOptions: [],
      loading: false,
      total: 1,
      tableData: [
        {
          ldmc: "永康 2025 转027号",
          ldlx: "WEB漏洞",
          lddj: "中危",
          ldip: "nlpt.yk.gov.cn",
          lddk: "9061",
          ldsjfw: "漏洞涉及的服务",
          ldsjyy: "永康市一网统管",
          dwmc: "永康市大数据发展中心",
          qssj: "2025-05-22 00:00:00",
          gxsj: "2025-05-27 17:15:37",
        },
        {
          ldmc: "永康 2025 转027号",
          ldlx: "WEB漏洞",
          lddj: "中危",
          ldip: "nlpt.yk.gov.cn",
          lddk: "9061",
          ldsjfw: "漏洞涉及的服务",
          ldsjyy: "永康市一网统管",
          dwmc: "永康市大数据发展中心",
          qssj: "2025-05-22 00:00:00",
          gxsj: "2025-05-27 17:15:37",
        },
        {
          ldmc: "永康 2025 转027号",
          ldlx: "WEB漏洞",
          lddj: "中危",
          ldip: "nlpt.yk.gov.cn",
          lddk: "9061",
          ldsjfw: "漏洞涉及的服务",
          ldsjyy: "永康市一网统管",
          dwmc: "永康市大数据发展中心",
          qssj: "2025-05-22 00:00:00",
          gxsj: "2025-05-27 17:15:37",
        },
        {
          ldmc: "永康 2025 转027号",
          ldlx: "WEB漏洞",
          lddj: "中危",
          ldip: "nlpt.yk.gov.cn",
          lddk: "9061",
          ldsjfw: "漏洞涉及的服务",
          ldsjyy: "永康市一网统管",
          dwmc: "永康市大数据发展中心",
          qssj: "2025-05-22 00:00:00",
          gxsj: "2025-05-27 17:15:37",
        },
        {
          ldmc: "永康 2025 转027号",
          ldlx: "WEB漏洞",
          lddj: "中危",
          ldip: "nlpt.yk.gov.cn",
          lddk: "9061",
          ldsjfw: "漏洞涉及的服务",
          ldsjyy: "永康市一网统管",
          dwmc: "永康市大数据发展中心",
          qssj: "2025-05-22 00:00:00",
          gxsj: "2025-05-27 17:15:37",
        },
      ],
      //弹窗
      show: false,
      info: {},
    };
  },
  mounted() {
    this.getCurrentMonth();
    this.getList();
  },
  methods: {
    changeTab(i) {
      this.tabIndex = i;
    },
    getList() {
      // console.log("Search keyword:", this.searchKeyword);
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        gzbt: undefined,
        gjjb: undefined,
        gjdx: undefined,
      };
    },
    handleAdd() {},
    handleDelete() {},
    handleImport() {},
    handleExport() {},
    batchInform() {},
    handleDetail(index, row) {
      this.show = true;
    },
    close() {
      this.show = false;
    },
    getCurrentMonth() {
      let dateStart = new Date().setDate(1);
      let month = new Date().getMonth();
      let dateEnd = new Date(new Date().setMonth(month + 1)).setDate(0);
      this.dateRange = [dateStart, dateEnd];
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  .card {
    width: 100%;
    border-radius: 15px;
    padding: 20px 20px;
    box-sizing: border-box;
    background-color: #fff;
    height: auto;
    margin-bottom: 12px;
  }
}
.timeSelector {
  position: absolute;
  right: 20px;
  top: -40px;
  justify-content: flex-end;
  & > div {
    margin-right: 12px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 14px;
    color: #1d2129;
    line-height: 22px;
    text-align: left;
  }
}
.tabList {
  padding: 0 40px;
  box-sizing: border-box;
  .tabCon {
    display: flex;
    align-items: center;
    width: 100%;
    &:last-child {
      width: 150px;
    }
  }
  .tab {
    position: relative;
    width: 150px;
    height: 70px;
    background: #f9fafb;
    border-radius: 35px 35px 35px 35px;
    border: 1px solid #eff0f1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    .name {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 16px;
      color: #1d2129;
      line-height: 22px;
    }
    .num {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 14px;
      color: #0057fe;
      line-height: 22px;
      text-align: center;
    }
    // &::after {
    //   content: "";
    //   position: absolute;
    //   top: 0px;
    //   right: -40px;
    //   width: 20px;
    //   height: 21px;
    //   background: url("~@/assets/images/warning/arrow-right.png");
    // }
  }
  .imgBox {
    width: calc(100% - 150px);
    .icon {
      width: 20px;
      height: 21px;
    }
  }
  .tab_active {
    background: rgba(0, 87, 254, 0.1);
    border: 1px solid #0057fe;
  }
}
.tag {
  width: fit-content;
  padding: 4px 12px;
  box-sizing: border-box;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  border-radius: 20px;
}
.tag_red {
  background: #ff00001a;
  color: #ff0000;
}
.tag_yel {
  background: #ffca3a1a;
  color: #ffca3a;
}
.tag_org {
  background: #ff7d001a;
  color: #ff7d00;
}
</style>
