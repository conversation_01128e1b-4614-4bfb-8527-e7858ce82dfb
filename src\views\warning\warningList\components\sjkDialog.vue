<template>
  <el-dialog title="数据库预警详情" :visible="show" width="65%" @close="close">
    <div class="info">
      <div class="line">
        <div class="label">工单编号</div>
        <div class="value text_link" v-if="info.gdId" @click="goDetail">
          {{ info.gdNo }}
        </div>
        <div class="value" v-else>-</div>
      </div>
      <div class="line">
        <div class="label">关联部门</div>
        <div class="value">{{ info.deptName || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">关联应用</div>
        <div class="value">{{ info.yyName || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">告警等级</div>
        <div class="value">
          {{ info.level == 2 ? "紧急" : info.level == 1 ? "一般" : "-" }}
        </div>
      </div>
      <div class="line">
        <div class="label">告警时间</div>
        <div class="value">{{ info.createTime || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">告警状态</div>
        <div class="value">
          {{ info.status == 1 ? "已完成" : info.status == 0 ? "未完成" : "-" }}
        </div>
      </div>
      <div class="line">
        <div class="label">告警内容</div>
        <div class="value">{{ info.content || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">处置方式</div>
        <div class="value">
          {{ info.czfs == 1 ? "忽略" : info.czfs == 2 ? "转工单" : "" }}
        </div>
      </div>
      <div class="line">
        <div class="label">主机IP</div>
        <div class="value">{{ info.ip || "-" }}</div>
      </div>

      <div class="line">
        <div class="label">数据库id</div>
        <div class="value">{{ info.dbId || "-" }}</div>
      </div>

      <div class="line">
        <div class="label">实例名称</div>
        <div class="value">{{ info.instance || "-" }}</div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    goDetail() {
      this.$router.push({
        path: "/serve/workDispose",
        query: { id: this.info.gdId,yyId:this.info.yyId },
      });
    },
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
.info {
  padding: 20px;
  box-sizing: border-box;
  .line {
    display: table;
    width: 100%;
    table-layout: fixed;
    &:last-child {
      border-bottom: 1px solid #eff0f1;
    }
    .label {
      display: table-cell;
      width: 200px;
      background: #f9fafb;
      border: 1px solid #eff0f1;
      border-bottom: none;
      border-right: none;
      box-sizing: border-box;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      vertical-align: top;
      padding: 8px 21px;
    }
    .value {
      display: table-cell;
      vertical-align: top;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      color: #667085;
      border: 1px solid #eff0f1;
      border-bottom: none;
      text-align: left;
      box-sizing: border-box;
      padding: 8px 21px;
      background: #fff;
      word-break: break-all;
    }
  }
}
::v-deep .el-dialog {
  border-radius: 12px;
  overflow: hidden;
  .el-dialog__body {
    padding: 0;
    max-height: 640px;
    overflow-y: scroll;
    padding-bottom: 20px;
    margin-bottom: 20px;
  }
}
</style>