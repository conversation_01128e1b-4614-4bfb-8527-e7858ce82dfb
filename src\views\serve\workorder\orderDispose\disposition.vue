<template>
  <div>
    <div class="list flex-c">
      <div class="li" v-for="(item, i) in list" :key="i" @click="go(item)">
        <img :src="item.icon" class="icon" />
        <div class="name">{{ item.name }}</div>
        <div class="des">{{ item.des }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [
        {
          name: "服务器监控",
          des: "实时监控服务器资源使用情况",
          icon: require("@/assets/images/serve/fwqjk.png"),
          url: "",
        },
        {
          name: "性能分析",
          des: "分析服务器性能瓶颈",
          icon: require("@/assets/images/serve/xnfx.png"),
          url: "",
        },
        {
          name: "资源管理",
          des: "临时扩展服务器资源",
          icon: require("@/assets/images/serve/zygl.png"),
          url: "",
        },
        {
          name: "负载均衡",
          des: "调整负载均衡配置",
          icon: require("@/assets/images/serve/fzjh.png"),
          url: "",
        },
        {
          name: "日志分析",
          des: "分析服务器日志排查问题",
          icon: require("@/assets/images/serve/rzfx.png"),
          url: "",
        },
        {
          name: "网络诊断",
          des: "诊断网络连接问题",
          icon: require("@/assets/images/serve/wlzd.png"),
          url: "",
        },
        {
          name: "知识库查询",
          des: "知识库中查询相关知识",
          icon: require("@/assets/images/serve/zskcx.png"),
          url: "",
        },
        {
          name: "缓存管理",
          des: "管理应用缓存提高性能",
          icon: require("@/assets/images/serve/hcgl.png"),
          url: "",
        },
      ],
    };
  },
  methods: {
    go(item) {},
  },
};
</script>

<style lang="scss" scoped>
.list {
  flex-wrap: wrap;
  padding: 20px;
  box-sizing: border-box;
  .li {
    width: calc((100% - 24 * 3px) / 4);
    margin-right: 24px;
    margin-bottom: 24px;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 18px;
    padding: 20px 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    &:nth-child(4n + 4) {
      margin-right: 0;
    }
    .icon {
      width: 30px;
      height: 30px;
    }
    .name {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #1f2937;
      line-height: 24px;
      margin-top: 8px;
    }
    .des {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      color: #6b7280;
      line-height: 16px;
      margin-top: 4px;
    }
  }
}
</style>
