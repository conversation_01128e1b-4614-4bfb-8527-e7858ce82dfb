<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-07-10 08:57:30
 * @LastEditors: wjb
 * @LastEditTime: 2025-10-23 19:03:20
-->
<template>
  <div class="container">
    <div class="flex-stretch">
      <div class="card" style="width: 75%; margin-right: 12px">
        <div class="cardTitle">运维概览</div>
        <ywgl></ywgl>
      </div>
      <div class="card" style="width: 24%">
        <div class="flex-c">
          <div
            class="titleTab2"
            v-for="(x, i) in titleTabList"
            :key="i"
            :class="tabIndex == i ? 'titleTab' : ''"
            @click="changeTab(i)"
          >
            {{ x }}
          </div>
        </div>
        <dbsx :tabIndex="tabIndex"></dbsx>
      </div>
    </div>
    <div class="card">
      <div class="flex-b">
        <div class="cardTitle">应用清单</div>
        <div class="flex-c">
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
          >
            <el-form-item prop="name">
              <el-input
                v-model="queryParams.name"
                placeholder="请输入应用名称"
                clearable
                style="width: 160px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item prop="deptId">
              <treeselect
                style="width: 160px; height: 32px"
                v-model="queryParams.deptId"
                :options="enabledDeptOptions"
                :show-count="true"
                placeholder="请选择归属部门"
              />
            </el-form-item>
            <!-- <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                @click="handleQuery"
              >
                查询
              </el-button>
              <el-button icon="el-icon-refresh" @click="resetQuery"
                >重置</el-button
              >
            </el-form-item> -->
          </el-form>
        </div>
      </div>
      <yyqd :name="queryParams.name" :deptId="queryParams.deptId"></yyqd>
    </div>
    <div class="flex-stretch">
      <div
        class="card"
        v-if="type == 4 || type == 5"
        style="width: 33%; margin-right: 12px"
      >
        <div class="cardTitle">运维子系统</div>
        <div class="itemList">
          <div
            class="item"
            v-for="(item, i) in zxtList"
            :key="i"
            @click="jumpUrlTo(item)"
          >
            <img :src="item.icon" class="icon" />
            <div class="name">{{ item.name }}</div>
          </div>
        </div>
      </div>
      <div
        class="card"
        v-if="type == 1 || type == 3"
        style="width: 33%; margin-right: 12px"
      >
        <div class="cardTitle">值班人员</div>
        <div class="duty-personnel">
          <div class="duty-container">
            <!-- 值班负责人 -->
            <div class="duty-leader">
              <img src="@/assets/images/home/<USER>" alt="" />
              <div class="duty-info">
                <div class="duty-role">值班负责人</div>
                <div class="duty-name">{{ dutyLeader.name }}</div>
                <div class="duty-phone">{{ dutyLeader.phone }}</div>
              </div>
            </div>

            <!-- 其他值班人员 -->
            <div class="duty-members">
              <div
                class="duty-member"
                v-for="(member, index) in dutyMembers"
                :key="index"
              >
                <img src="@/assets/images/home/<USER>" alt="" />
                <div class="member-info">
                  <div class="member-name">{{ member.name }}</div>
                  <div class="member-phone">{{ member.phone }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="card"
        v-if="type == 2 || type == 4 || type == 5"
        style="width: 33%; margin-right: 12px"
      >
        <div class="cardTitle">运维工具</div>
        <div class="itemList">
          <div
            class="item"
            v-for="(item, i) in ywgjList"
            :key="i"
            @click="jumpUrl(item)"
          >
            <img :src="item.icon" class="icon" />
            <div class="name">{{ item.name }}</div>
          </div>
        </div>
      </div>
      <div
        class="card"
        v-if="type == 2 || type == 3"
        style="width: 33%; margin-right: 12px"
      >
        <div class="cardTitle">
          <div>待办工单</div>
          <div class="text_link_1" @click="showDialog">新增工单</div>
        </div>
        <div style="height: 200px; overflow-y: auto">
          <el-table :data="tableData" style="width: 100%">
            <el-table-column prop="yyName" label="应用/项目" align="center">
            </el-table-column>
            <el-table-column
              prop="title"
              label="工单名称"
              align="center"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column prop="handlerName" label="处理人" align="center">
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-button @click="handleDetail(scope.row)" type="text">
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="flex-c-c" style="margin-top: 24px">
            <div class="text_link" @click="goMore">查看更多</div>
          </div>
        </div>
      </div>
      <div class="card" v-if="type == 1" style="width: 33%; margin-right: 12px">
        <div class="cardTitle">
          <div>工单处置情况TOP5</div>
          <div class="time-tabs">
            <span
              :class="['time-tab', { active: workOrderTimeRange === 1 }]"
              @click="switchWorkOrderTime(1)"
            >
              近一周
            </span>
            <span
              :class="['time-tab', { active: workOrderTimeRange === 2 }]"
              @click="switchWorkOrderTime(2)"
            >
              近一个月
            </span>
          </div>
        </div>
        <div class="work-order-chart">
          <div class="chart-container" ref="workOrderChart"></div>
        </div>
      </div>
      <div class="card" style="width: 33%" v-if="type == 4 || type == 5">
        <div class="cardTitle">
          快捷方式
          <span style="cursor: poniter" @click="showEdit = !showEdit">
            <i class="el-icon-edit"></i>
          </span>
        </div>
        <div class="itemList">
          <div class="item" v-for="(item, i) in kjfsList" :key="i">
            <img :src="item.icon" class="icon" />
            <img
              src="@/assets/images/ywmh/close_red.png"
              class="btn"
              v-if="showEdit"
              @click="editShortcuts(i, item)"
            />
            <div class="name">{{ item.name }}</div>
          </div>
        </div>
      </div>
      <div
        class="card"
        style="width: 33%; margin-right: 12px"
        v-if="type == 1 || type == 2 || type == 3"
      >
        <div class="cardTitle">
          <div>处置预案</div>
          <div class="text_link_1" @click="lookMoreCzya">查看更多</div>
        </div>
        <div class="itemList">
          <div
            class="item"
            v-for="(item, i) in czyaList"
            :key="i"
            @click="jumpCzya(item)"
          >
            <img :src="item.icon" class="icon" />
            <div class="name max_line2">{{ item.name }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 处置预案对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="780px" append-to-body>
      <el-form ref="form" :model="form" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="预案名称" prop="name">
              <el-input
                :disabled="lookFlag"
                v-model="form.name"
                placeholder="请输入预案名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预案场景描述" prop="cjms">
              <el-input
                :disabled="lookFlag"
                v-model="form.cjms"
                type="textarea"
                rows="4"
                placeholder="请输入预案场景描述"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="lxr">
              <el-input
                :disabled="lookFlag"
                v-model="form.lxr"
                placeholder="请输入联系人"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式" prop="lxfs">
              <el-input
                :disabled="lookFlag"
                v-model="form.lxfs"
                placeholder="请输入联系方式"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预案内容" prop="content">
              <editor
                :readOnly="lookFlag"
                v-model="form.content"
                :min-height="192"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>

    <!-- 弹窗 -->
    <addOrderDialog
      :show="show"
      :info="info"
      @close="show = false"
      @addSuccess="addSuccess"
    ></addOrderDialog>
  </div>
</template>

<script>
import ywgl from "./ywgl/index.vue";
import dbsx from "./dbsx/index.vue";
import yyqd from "./yyqd/index.vue";
import { getDddlUrl } from "@/api/login";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { listAllYy, deptTreeSelect } from "@/api/serve/orderlist";
import * as echarts from "echarts";
import { getUserRoleMax, getGdczqkTop5, getDbList } from "@/api/home/<USER>";
import { listCzya, getCzya } from "@/api/warning/planManage";
import addOrderDialog from "@/views/warning/warningList/components/addOrderDialog.vue";

export default {
  components: { ywgl, dbsx, yyqd, Treeselect, addOrderDialog },
  data() {
    return {
      tabIndex: 0,
      titleTabList: ["待办事项", "通知公告"],
      // 值班人员数据
      dutyLeader: {
        name: "舒巧龙",
        phone: "15868903360",
      },
      dutyMembers: [
        {
          name: "陈能帅",
          phone: "15757945425",
        },
        {
          name: "严彬",
          phone: "15957186329",
        },
      ],
      // 工单处置情况数据
      workOrderTimeRange: 1, // 'week' 或 'month'
      workOrderChart: null,
      workOrderData: [],
      //应用清单
      queryParams: {
        name: undefined,
        deptId: undefined,
      },
      appOptions: [],
      enabledDeptOptions: [],
      //运维子系统
      zxtList: [
        {
          name: "云管平台",
          url: "",
          icon: require("@/assets/images/ywmh/ygpt.png"),
        },
        {
          name: "风暴中心",
          url: "https://172.24.140.36",
          icon: require("@/assets/images/ywmh/fbzx.png"),
        },
        {
          name: "OSM数据库监控平台",
          url: "https://39.175.50.240:18020/",
          icon: require("@/assets/images/ywmh/sjaqzxpt.png"),
        },
        // {
        //   name: "端点检测平台",
        //   url: "",
        //   icon: require("@/assets/images/ywmh/ddjcpt.png"),
        // },
      ],
      //运维工具
      ywgjList: [
        {
          name: "易代码",
          url: "",
          icon: require("@/assets/images/ywmh/pljbzx.png"),
        },
        {
          name: "易部署",
          url: "",
          icon: require("@/assets/images/ywmh/pzjcgj.png"),
        },
        {
          name: "易感知",
          url: "http://39.175.50.240:18085/login",
          icon: require("@/assets/images/ywmh/zdhbs.png"),
        },
        {
          name: "容器云",
          url: "",
          icon: require("@/assets/images/ywmh/gzzdgj.png"),
        },
        {
          name: "harbor",
          url: "https://39.175.50.240:1443/",
          icon: require("@/assets/images/ywmh/harbor.png"),
        },
      ],
      //快捷方式
      kjfsList: [
        {
          name: "工单提交",
          url: "",
          icon: require("@/assets/images/ywmh/gdtj.png"),
        },
        {
          name: "监控大屏",
          url: "",
          icon: require("@/assets/images/ywmh/jkdp.png"),
        },
        {
          name: "资产查询",
          url: "",
          icon: require("@/assets/images/ywmh/zccx.png"),
        },
        {
          name: "备份管理",
          url: "",
          icon: require("@/assets/images/ywmh/bfgl.png"),
        },
      ],
      showEdit: false,
      type: 0, //用户角色，1数据局，2超管和运维管理员，3应用管理员，4运维人员，5，其他
      //处置预案
      czyaList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      // 表单参数
      form: {
        name: undefined,
        lxfs: undefined,
        lxr: undefined,
        content: undefined,
        cjms: undefined,
      },
      lookFlag: false,
      //新增工单
      show: false,
      info: {},
      tableData: [],
    };
  },
  mounted() {
    this.initData();
  },

  methods: {
    initData() {
      listAllYy().then((res) => {
        this.appOptions = res.data;
      });
      /** 查询部门下拉树结构 */
      deptTreeSelect().then((response) => {
        this.enabledDeptOptions = this.filterDisabledDept(
          JSON.parse(JSON.stringify(response.data))
        );
      });
      this.getList();
      getUserRoleMax().then((res) => {
        this.type = res.data.roleType;
      });
      this.getTop5();
      listCzya({ pageNum: 1, pageSize: 4 }).then((response) => {
        var czyaArr = response.data.list;
        if (czyaArr.length == 1) {
          this.czyaList = [
            {
              name: czyaArr[0].name,
              id: czyaArr[0].id,
              icon: require("@/assets/images/home/<USER>"),
            },
          ];
        } else if (czyaArr.length == 2) {
          this.czyaList = [
            {
              name: czyaArr[0].name,
              id: czyaArr[0].id,
              icon: require("@/assets/images/home/<USER>"),
            },
            {
              name: czyaArr[1].name,
              id: czyaArr[1].id,
              icon: require("@/assets/images/home/<USER>"),
            },
          ];
        } else if (czyaArr.length == 3) {
          this.czyaList = [
            {
              name: czyaArr[0].name,
              id: czyaArr[0].id,
              icon: require("@/assets/images/home/<USER>"),
            },
            {
              name: czyaArr[1].name,
              id: czyaArr[1].id,
              icon: require("@/assets/images/home/<USER>"),
            },
            {
              name: czyaArr[2].name,
              id: czyaArr[2].id,
              icon: require("@/assets/images/home/<USER>"),
            },
          ];
        } else if (czyaArr.length == 4) {
          this.czyaList = [
            {
              name: czyaArr[0].name,
              id: czyaArr[0].id,
              icon: require("@/assets/images/home/<USER>"),
            },
            {
              name: czyaArr[1].name,
              id: czyaArr[1].id,
              icon: require("@/assets/images/home/<USER>"),
            },
            {
              name: czyaArr[2].name,
              id: czyaArr[2].id,
              icon: require("@/assets/images/home/<USER>"),
            },
            {
              name: czyaArr[3].name,
              id: czyaArr[3].id,
              icon: require("@/assets/images/home/<USER>"),
            },
          ];
        }
      });
    },
    // 过滤禁用的部门
    filterDisabledDept(deptList) {
      return deptList.filter((dept) => {
        if (dept.disabled) {
          return false;
        }
        if (dept.children && dept.children.length) {
          dept.children = this.filterDisabledDept(dept.children);
        }
        return true;
      });
    },
    jumpUrlTo(item) {
      if (item.name == "云管平台") {
        getDddlUrl({ name: "电信云平台" }).then((res) => {
          if (res.code == 200) {
            window.open(res.msg);
          }
        });
      } else {
        window.open(item.url);
      }
    },
    jumpUrl(item) {
      if (item.name == "易代码") {
        getDddlUrl({ name: "联调大运维平台", mk: "GIT" }).then((res) => {
          if (res.code == 200) {
            window.open(res.msg);
          }
        });
      } else if (item.name == "易部署") {
        getDddlUrl({ name: "联调大运维平台", mk: "JK" }).then((res) => {
          if (res.code == 200) {
            window.open(res.msg);
          }
        });
      } else if (item.name == "容器云") {
        getDddlUrl({ name: "联调大运维平台", mk: "KUBE" }).then((res) => {
          if (res.code == 200) {
            window.open(res.msg);
          }
        });
      } else {
        window.open(item.url);
      }
    },
    changeTab(i) {
      this.tabIndex = i;
    },
    editShortcuts(i) {
      this.$confirm("确定要删除该快捷方式吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          console.log(i);
          this.kjfsList.splice(i, 1);
        })
        .catch(() => {});
    },
    // 获取值班人员数据
    getDutyPersonnel() {
      // 这里可以调用API获取实际的值班人员数据
      // 示例：
      // getDutyPersonnelApi().then(res => {
      //   if (res.code === 200) {
      //     this.dutyLeader = res.data.leader;
      //     this.dutyMembers = res.data.members;
      //   }
      // });

      // 模拟数据更新
      this.dutyLeader = {
        name: "张三",
        phone: "13300000001",
      };
      this.dutyMembers = [
        {
          name: "李四",
          phone: "13300000002",
        },
        {
          name: "王五",
          phone: "13300000003",
        },
      ];
    },
    //工单top5开始
    getTop5() {
      getGdczqkTop5({ type: this.workOrderTimeRange }).then((res) => {
        this.workOrderData = res.data;
        this.initWorkOrderChart();
      });
    },
    // 切换工单时间范围
    switchWorkOrderTime(range) {
      this.workOrderTimeRange = range;
      this.getTop5();
    },
    // 初始化工单图表
    initWorkOrderChart() {
      if (this.workOrderChart) {
        this.workOrderChart.dispose();
      }

      const chartDom = this.$refs.workOrderChart;
      if (!chartDom) return;

      this.workOrderChart = echarts.init(chartDom);

      const currentData = this.workOrderData;
      console.log("111", currentData);

            // 根据最大值设置Y轴配置
      let yAxisConfig = {
        type: "value",
        name: "单位：个",
        axisLabel: {
          color: "#667085",
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: "#DEE3E9",
            type: "dotted",
          },
        },
      };
      
      // 当最大值小于等于5时，设置刻度为整数
      if (maxValue <= 5) {
        yAxisConfig.splitNumber = maxValue;
        yAxisConfig.max = 5;
        yAxisConfig.interval = 1;
      } else {
        // 当最大值大于5时，正常显示
        yAxisConfig.splitNumber = 4;
      }

      const option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          data: ["生产工单", "解决工单"],
          top: 0,
          right: 10,
          itemWidth: 12,
          itemHeight: 12,
          textStyle: {
            fontSize: 12,
            color: "#666",
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "0",
          top: "20%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: currentData.map((item) => item.name),
          axisLabel: {
            fontSize: 14,
            color: "rgba(102,112,133,0.8)",
            interval: 0,
            rotate: 0,
          },
          axisTick: {
            alignWithLabel: true,
          },
        },
        yAxis: yAxisConfig,
        series: [
          {
            name: "生产工单",
            type: "bar",
            data: currentData.map((item) => item.gdzs),
            itemStyle: {
              color: "#3AA1FF",
            },
            barWidth: "20%",
          },
          {
            name: "解决工单",
            type: "bar",
            data: currentData.map((item) => item.yjjgds),
            itemStyle: {
              color: "#F98E1B",
            },
            barWidth: "20%",
          },
        ],
      };

      this.workOrderChart.setOption(option);

      // 监听窗口大小变化
      window.addEventListener("resize", () => {
        if (this.workOrderChart) {
          this.workOrderChart.resize();
        }
      });
    },
    //工单top5结束
    //处置预案开始
    jumpCzya(item) {
      this.reset();
      const noticeId = item.id;
      getCzya({ id: noticeId }).then((response) => {
        this.form = response.data;
        this.lookFlag = true;
        this.open = true;
        this.title = "查看预案";
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        lxfs: undefined,
        lxr: undefined,
        content: undefined,
        cjms: undefined,
      };
      this.resetForm("form");
    },
    lookMoreCzya() {
      this.$router.push({ path: "/warning/planManage" });
    },
    //处置预案结束
    //新增工单开始--
    getList() {
      getDbList({ pageNum: 1, pageSize: 10 }).then((res) => {
        this.tableData = res.data.list;
      });
    },
    handleDetail(row) {
      this.$router.push({ path: "/serve/workDispose", query: { id: row.id,yyId:row.yyId } });
    },
    showDialog() {
      this.show = true;
    },
    addSuccess() {
      this.show = false;
      this.getList();
    },
    //新增工单结束--
    goMore() {
      this.$router.push({
        path: "/serve/workorder",
      });
    },
  },
  beforeDestroy() {
    if (this.workOrderChart) {
      this.workOrderChart.dispose();
    }
  },
};
</script>

<style lang="scss" scoped>
.container {
  // padding: 0px 20px;
  padding-bottom: 40px;
  box-sizing: border-box;
}
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;
  .cardTitle {
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: space-between;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-right: 30px;
    margin-bottom: 10px;
    .text_link_1 {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 14px;
      color: #0057fe;
      line-height: 22px;
      cursor: pointer;
    }
  }
  .titleTab2 {
    color: #667085;
    font-weight: 500;
    font-size: 18px;
    line-height: 24px;
    text-align: left;
    margin-right: 30px;
    cursor: pointer;
  }
  .titleTab {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-right: 30px;
    cursor: pointer;
  }
}
.itemList {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  .item {
    text-align: center;
    cursor: pointer;
    position: relative;
  }
  .icon {
    width: 40px;
    height: 40px;
    margin-bottom: 10px;
  }
  .name {
    max-width: 6em;
    font-size: 14px;
    color: #4e5969;
    line-height: 20px;
    text-align: center;
  }
  .btn {
    position: absolute;
    right: 2px;
    top: -4px;
    width: 16px;
    height: 16px;
    z-index: 2;
  }
}
.flex-stretch {
  display: flex;
  align-items: stretch;
}
::v-deep .vue-treeselect__control {
  height: 32px;
}

/* 值班人员样式 */

.duty-container {
  display: flex;
  justify-content: center;
  align-content: center;
  align-items: center;
}

.duty-leader {
  display: flex;
  align-items: center;
  border-radius: 8px;
  text-align: center;
  img {
    width: 110px;
    height: 110px;
  }
  .duty-info {
    width: 120px;
    .duty-role {
      height: 37px;
      background: #f9fafb;
      border: 1px solid #e8ebf1;
      font-size: 14px;
      color: #1d2129;
      box-sizing: border-box;
      line-height: 38px;
      padding-left: 14px;
      text-align: left;
    }

    .duty-name {
      height: 37px;
      background: #ffffff;
      border: 1px solid #e8ebf1;
      border-top: none;
      font-size: 14px;
      color: #4e5969;
      box-sizing: border-box;
      line-height: 37px;
      padding-left: 14px;
      text-align: left;
    }

    .duty-phone {
      height: 36px;
      background: #ffffff;
      border: 1px solid #e8ebf1;
      border-top: none;
      font-size: 14px;
      color: #4e5969;
      box-sizing: border-box;
      line-height: 37px;
      padding-left: 14px;
      text-align: left;
    }
  }
}

.duty-members {
  margin-left: 24px;
  .duty-member {
    display: flex;
    img {
      width: 55px;
      height: 55px;
    }

    .member-info {
      width: 120px;
      .member-name {
        height: 28px;
        background: #ffffff;
        border: 1px solid #e8ebf1;
        font-size: 14px;
        color: #4e5969;
        box-sizing: border-box;
        line-height: 28px;
        padding-left: 14px;
        text-align: left;
      }

      .member-phone {
        height: 27px;
        background: #ffffff;
        border: 1px solid #e8ebf1;
        border-top: none;
        font-size: 14px;
        color: #4e5969;
        box-sizing: border-box;
        line-height: 27px;
        padding-left: 14px;
        text-align: left;
      }
    }
  }
}

/* 工单处置情况样式 */
.time-tabs {
  display: flex;
  gap: 16px;
}

.time-tab {
  font-size: 12px;
  color: #999;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    color: #409eff;
    background-color: #ecf5ff;
  }

  &.active {
    color: #409eff;
    background-color: #ecf5ff;
    font-weight: 500;
  }
}

.work-order-chart {
  margin-top: 16px;
}

.chart-container {
  width: 100%;
  height: 140px;
}

/* 优化卡片标题布局 */
.cardTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
</style>
