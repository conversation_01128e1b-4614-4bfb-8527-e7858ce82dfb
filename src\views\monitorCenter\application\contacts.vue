<template>
  <div class="wrap flex-c">
    <div class="line">
      <div class="label">姓名</div>
      <div class="value">{{ data.xm || "-" }}</div>
    </div>
    <div class="line">
      <div class="label">家庭住址</div>
      <div class="value">{{ data.jtzz || "-" }}</div>
    </div>
    <div class="line">
      <div class="label">职位</div>
      <div class="value">{{ data.zw || "-" }}</div>
    </div>
    <div class="line">
      <div class="label">联系方式</div>
      <div class="value">{{ data.lxfs || "-" }}</div>
    </div>
    <div class="line">
      <div class="label">性别</div>
      <div class="value">{{ data.xb || "-" }}</div>
    </div>
    <div class="line">
      <div class="label">所属单位</div>
      <div class="value">{{ data.ssdw || "--" }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style lang="scss" scoped>
.wrap {
  flex-wrap: wrap;
}
.line {
  display: flex;
  align-items: center;
  width: 34%;
  margin: 6px 20px 16px 20px;
  .label {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #4e5969;
    line-height: 20px;
    text-align: left;
    width: 120px;
    margin-right: 16px;
    white-space: nowrap;
  }
  .value {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #1d2129;
    line-height: 20px;
    text-align: left;
    width: 100%;
  }
}
</style>
