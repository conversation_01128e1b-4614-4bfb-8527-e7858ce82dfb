<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="预案名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入预案名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-bell"
          size="mini"
          @click="handleNotice"
          >一键通知</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-video-camera"
          size="mini"
          @click="handleCall"
          >一键会商</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="noticeList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" width="100" />
      <el-table-column
        label="预案名称"
        align="center"
        prop="name"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="场景" align="center" prop="cjms" />
      <el-table-column label="属地" align="center" prop="xsq" />
      <el-table-column label="应急通讯录" align="center" prop="createTime">
        <template slot-scope="scope">
          <div
            style="display: flex; align-items: center; justify-content: center"
          >
            <span>{{ formatName(scope.row.lxr, scope.row.id) }}</span>
            <!-- 姓名显示/隐藏控制 -->
            <span style="margin-left: 8px">{{
              formatPhone(scope.row.lxfs, scope.row.id)
            }}</span>
            <!-- 手机号显示/隐藏控制 -->
            <i
              v-if="phoneVisibilityMap[scope.row.id]"
              class="el-icon-view"
              style="
                margin-left: 4px;
                cursor: pointer;
                color: #409eff;
                font-size: 12px;
              "
              @click="togglePhoneVisibility(scope.row.id)"
              title="隐藏手机号"
            ></i>
            <i
              v-else
              class="custom-eye-hide"
              style="
                margin-left: 4px;
                cursor: pointer;
                color: #409eff;
                font-size: 12px;
              "
              @click="togglePhoneVisibility(scope.row.id)"
              title="显示手机号"
            ></i>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleLook(scope.row)"
            >查看</el-button
          >
          <el-button size="mini" type="text" @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button size="mini" type="text" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="780px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="预案名称" prop="name">
              <el-input
                :disabled="lookFlag"
                v-model="form.name"
                maxlength="20"
                placeholder="请输入预案名称"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="预案场景描述" prop="cjms">
              <el-input
                :disabled="lookFlag"
                v-model="form.cjms"
                type="textarea"
                rows="4"
                placeholder="请输入预案场景描述"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="联系人" prop="lxr">
              <el-input
                :disabled="lookFlag"
                v-model="form.lxr"
                placeholder="请输入联系人"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式" prop="lxfs">
              <el-input
                :disabled="lookFlag"
                v-model="form.lxfs"
                placeholder="请输入联系方式"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="属地" prop="xsq">
              <el-select v-model="form.xsq" placeholder="请选择属地">
                <el-option
                  v-for="item in xsqOptions"
                  :key="item.xsq"
                  :label="item.xsq"
                  :value="item.xsq"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="预案内容" prop="content">
              <editor
                :readOnly="lookFlag"
                v-model="form.content"
                :min-height="192"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" v-if="title != '查看预案'"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 一键通知 对话框-->
    <el-dialog
      :title="title1"
      :visible.sync="open1"
      width="780px"
      append-to-body
    >
      <el-form ref="form1" :model="form1" :rules="rules1" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="通知人" prop="phones">
              <el-select
                v-model="form1.phones"
                multiple
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in csrOptions"
                  :key="i"
                  :value="item.value"
                  :label="item.label"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="通知内容" prop="msg">
              <el-input
                v-model="form1.msg"
                type="textarea"
                rows="4"
                placeholder="请输入通知内容"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm1">确 定</el-button>
        <el-button @click="cancel1">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 一键会商 对话框-->
    <el-dialog
      :title="title2"
      :visible.sync="open2"
      width="780px"
      append-to-body
    >
      <el-form ref="form2" :model="form2" :rules="rules2" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="会议标题" prop="title">
              <el-input v-model="form2.title" placeholder="请输入会议标题" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="会商人员" prop="userIds">
              <el-select
                v-model="form2.userIds"
                multiple
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in csrOptions"
                  :key="i"
                  :value="item.value"
                  :label="item.label"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm2">确 定</el-button>
        <el-button @click="cancel2">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listCzya,
  getCzya,
  delCzya,
  addCzya,
  updateCzya,
  sendMsg,
  createMeeting,
} from "@/api/warning/planManage";
import { getUsercsList } from "@/api/serve/orderlist";

export default {
  name: "Notice",
  dicts: ["sys_notice_status", "sys_notice_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      noticeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
      },
      // 表单参数
      form: {
        name: undefined,
        lxfs: undefined,
        lxr: undefined,
        content: undefined,
        cjms: undefined,
        xsq: undefined,
      },
      // 属地选项
      xsqOptions: [
        { xsq: "市本级" },
        {
          xsq: "婺城区",
        },
        {
          xsq: "金东区",
        },
        {
          xsq: "兰溪市",
        },
        {
          xsq: "东阳市",
        },
        {
          xsq: "义乌市",
        },
        {
          xsq: "永康市",
        },
        {
          xsq: "浦江县",
        },
        {
          xsq: "武义县",
        },
        {
          xsq: "磐安县",
        },
      ],
      lookFlag: false,
      // 手机号码显示状态控制
      phoneVisibilityMap: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "预案名称不能为空", trigger: "blur" },
        ],
        lxr: [{ required: true, message: "联系人不能为空", trigger: "blur" }],
        lxfs: [
          { required: true, message: "联系方式不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
        content: [
          { required: true, message: "预案内容不能为空", trigger: "blur" },
        ],
        xsq: [{ required: true, message: "属地不能为空", trigger: "change" }],
      },
      // 一键通知弹出层标题
      title1: "",
      // 是否显示弹出层
      open1: false,
      // 表单参数
      form1: {
        msg: undefined,
        phones: [],
      },
      // 表单校验
      rules1: {
        msg: [{ required: true, message: "通知内容不能为空", trigger: "blur" }],
        phones: [
          { required: true, message: "通知人不能为空", trigger: "blur" },
        ],
      },
      csrOptions: [],
      // 一键会商弹出层标题
      title2: "",
      // 是否显示弹出层
      open2: false,
      // 表单参数
      form2: {
        title: undefined,
        userIds: [],
      },
      // 表单校验
      rules2: {
        title: [
          { required: true, message: "会议标题不能为空", trigger: "blur" },
        ],
        userIds: [
          { required: true, message: "会商人员不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.initData();
    this.getList();
  },
  computed: {
    phonenumber() {
      return this.$store.state.user.phonenumber;
    },
  },
  methods: {
    initData() {
      getUsercsList({ sfbhbr: 1 }).then((res) => {
        this.csrOptions = res.rows.map((item) => {
          return {
            phone: item.phonenumber,
            label: item.nickName + (item.gysName ? " - " + item.gysName : ""),
            value: item.userId,
          };
        });
      });
    },
    //一键通知开始
    handleNotice() {
      this.open1 = true;
      this.title1 = "一键通知";
    },
    /** 提交按钮 */
    submitForm1: function () {
      this.$refs["form1"].validate((valid) => {
        if (valid) {
          let arr = [];
          // 根据选中的value值，从csrOptions中提取对应的phone字段
          this.form1.phones.forEach((selectedValue) => {
            const matchedOption = this.csrOptions.find(
              (option) => option.value === selectedValue
            );
            if (matchedOption && matchedOption.phone) {
              arr.push(matchedOption.phone);
            }
          });

          var params = {
            phones: arr,
            msg: this.form1.msg,
          };
          sendMsg(params).then((response) => {
            this.$modal.msgSuccess("通知成功");
            this.open1 = false;
            this.getList();
          });
        }
      });
    },
    // 取消按钮
    cancel1() {
      this.open1 = false;
      this.reset1();
    },
    // 表单重置
    reset1() {
      this.form = {
        msg: undefined,
        phones: [],
      };
      this.resetForm("form1");
    },
    //一键通知结束
    //一键会商开始
    handleCall() {
      this.open2 = true;
      this.title2 = "一键会商";
    },
    /** 提交按钮 */
    submitForm2: function () {
      this.$refs["form2"].validate((valid) => {
        if (valid) {
          let arr = [];
          // 根据选中的value值，从csrOptions中提取对应的phone字段
          this.form2.userIds.forEach((selectedValue) => {
            const matchedOption = this.csrOptions.find(
              (option) => option.value === selectedValue
            );
            if (matchedOption && matchedOption.phone) {
              arr.push(matchedOption.phone);
            }
          });

          var params = {
            mobile: this.phonenumber,
            userIds: arr,
            title: this.form2.title,
          };
          createMeeting(params).then((response) => {
            this.$modal.msgSuccess("操作成功");
            this.open2 = false;
            this.getList();
          });
        }
      });
    },
    // 取消按钮
    cancel2() {
      this.open2 = false;
      this.reset2();
    },
    // 表单重置
    reset2() {
      this.form2 = {
        title: undefined,
        userIds: [],
      };
      this.resetForm("form2");
    },
    //一键会商结束
    /** 查询公告列表 */
    getList() {
      this.loading = true;
      listCzya(this.queryParams).then((response) => {
        this.noticeList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
        // 初始化显示状态（默认隐藏）
        this.initVisibility();
      });
    },
    // 初始化显示状态
    initVisibility() {
      const phoneVisibilityMap = {};
      this.noticeList.forEach((item) => {
        phoneVisibilityMap[item.id] = false; // 默认隐藏手机号
      });
      this.phoneVisibilityMap = phoneVisibilityMap;
    },
    // 格式化姓名显示（脱敏处理）
    formatName(name, id) {
      if (!name) return "";

      // 根据姓名显示状态决定是否脱敏
      if (this.phoneVisibilityMap[id]) {
        return name; // 显示完整姓名
      } else {
        // 姓名脱敏处理
        if (name.length === 1) {
          return name; // 单字姓名不脱敏
        } else if (name.length === 2) {
          return name.charAt(0) + "*"; // 两字姓名：张*
        } else if (name.length === 3) {
          return name.charAt(0) + "*" + name.charAt(2); // 三字姓名：张*三
        } else {
          // 四字及以上姓名：保留首尾，中间用*代替
          return (
            name.charAt(0) +
            "*".repeat(name.length - 2) +
            name.charAt(name.length - 1)
          );
        }
      }
    },
    // 格式化手机号码显示
    formatPhone(phone, id) {
      if (!phone) return "";

      // 检查是否为有效的手机号码格式
      if (!/^1[3-9]\d{9}$/.test(phone)) {
        return phone; // 如果不是标准手机号格式，直接返回原值
      }

      // 根据显示状态决定是否隐藏中间四位
      if (this.phoneVisibilityMap[id]) {
        return phone; // 显示完整手机号
      } else {
        return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2"); // 隐藏中间四位
      }
    },
    // 切换手机号码显示状态
    togglePhoneVisibility(id) {
      this.$set(this.phoneVisibilityMap, id, !this.phoneVisibilityMap[id]);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        lxfs: undefined,
        lxr: undefined,
        content: undefined,
        cjms: undefined,
        xsq: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.lookFlag = false;
      this.open = true;
      this.title = "新增预案";
    },
    /** 查看按钮操作 */
    handleLook(row) {
      this.reset();
      const noticeId = row.id;
      getCzya({ id: noticeId }).then((response) => {
        this.form = response.data;
        this.lookFlag = true;
        this.open = true;
        this.title = "查看预案";
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const noticeId = row.id || this.ids;
      getCzya({ id: noticeId }).then((response) => {
        this.form = response.data;
        this.lookFlag = false;
        this.open = true;
        this.title = "编辑预案";
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            updateCzya(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCzya(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const noticeIds = row.id ? [row.id] : this.ids;
      this.$modal
        .confirm("是否确认删除当前选中的数据项？")
        .then(function () {
          return delCzya({ ids: noticeIds.join(",") });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped>
/* 自定义闭眼图标 */
.custom-eye-hide {
  display: inline-block;
  width: 14px;
  height: 14px;
  position: relative;
  font-style: normal;
  background: url("~@/assets/images/eyes_hide.png") 0 0 no-repeat;
  background-size: cover;
}
</style>
</style>
