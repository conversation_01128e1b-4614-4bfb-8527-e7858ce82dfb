<template>
  <el-dialog title="隐患详情" :visible="show" width="65%" @close="close">
    <div class="info">
      <div class="line">
        <div class="label">CNCVE编号</div>
        <div class="value">{{ info.cncve || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">CNNVD编号</div>
        <div class="value">{{ info.cnnvd || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">CNVD编号</div>
        <div class="value">{{ info.cnvd || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">CVE编号</div>
        <div class="value">{{ info.cve || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">web漏洞测试数据</div>
        <div class="value">{{ info.webldcssj || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">事件来源</div>
        <div class="value">{{ info.sjly || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">任务ID</div>
        <div class="value">{{ info.rwid || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">区县</div>
        <div class="value">{{ info.qx || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">单位ID</div>
        <div class="value">{{ info.dwid || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">单位名称</div>
        <div class="value">{{ info.dwmc || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">单位类型</div>
        <div class="value">{{ info.dwlx || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">原始事件</div>
        <div class="value">{{ info.yssj || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">发现厂商</div>
        <div class="value">{{ info.fxcs || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">地址位置纬度</div>
        <div class="value">{{ info.wd || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">地址位置经度</div>
        <div class="value">{{ info.jd || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">城市</div>
        <div class="value">{{ info.cs || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">基线判断依据</div>
        <div class="value">{{ info.jxpdyj || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">基线检查项</div>
        <div class="value">{{ info.jxjcx || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">填报人</div>
        <div class="value">{{ info.tbr || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">填报时间</div>
        <div class="value">{{ info.tbsj || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">处置完成时间</div>
        <div class="value">{{ info.czwcsj || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">处置状态</div>
        <div class="value">{{ info.czzt || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">子任务历史ID</div>
        <div class="value">{{ info.zrwlsid || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">开始处置时间</div>
        <div class="value">{{ info.ksczsj || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">弱口令密码</div>
        <div class="value">{{ info.rklmm || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">弱口令用户名</div>
        <div class="value">{{ info.rklyhm || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">所属行业</div>
        <div class="value">{{ info.sshy || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">扫描器类型ID</div>
        <div class="value">{{ info.smqlxid || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">扫描器类型名称</div>
        <div class="value">{{ info.smqlxmc || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">数据来源</div>
        <div class="value">{{ info.sjly || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">更新时间</div>
        <div class="value">{{ info.gxsj || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">最后验证时间</div>
        <div class="value">{{ info.zhyzsj || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">涉及资产操作系统</div>
        <div class="value">{{ info.sjzcczxt || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">漏洞CVSS</div>
        <div class="value">{{ info.ldcvss || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">漏洞IP/域名</div>
        <div class="value">{{ info.ldip || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">漏洞Poc</div>
        <div class="value">{{ info.ldpoc || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">漏洞bug ID</div>
        <div class="value">{{ info.ldbugip || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">漏洞发布日期</div>
        <div class="value">{{ info.ldbfrq || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">漏洞所在URL</div>
        <div class="value">{{ info.ldszurl || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">漏洞描述</div>
        <div class="value">{{ info.ldms || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">漏洞插件ID</div>
        <div class="value">{{ info.ldcjid || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">漏洞涉及的协议</div>
        <div class="value">{{ info.ldsjdxy || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">漏洞涉及的应用</div>
        <div class="value">{{ info.ldsjdyy || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">漏洞端口</div>
        <div class="value">{{ info.lddk || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">漏洞等级</div>
        <div class="value">{{ info.lddj || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">漏洞类型</div>
        <div class="value">{{ info.ldlx || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">漏洞解决方案</div>
        <div class="value">{{ info.ldjjfa || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">省份</div>
        <div class="value">{{ info.sf || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">督办状态</div>
        <div class="value">{{ info.dbzt || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">结束时间</div>
        <div class="value">{{ info.jssj || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">资产ID</div>
        <div class="value">{{ info.zcid || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">起始时间</div>
        <div class="value">{{ info.qssj || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">通报编号</div>
        <div class="value">{{ info.tbbh || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">附件内容存储地址</div>
        <div class="value">{{ info.fjnrccdz || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">风险唯一ID</div>
        <div class="value">{{ info.fxwyid || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">验证人</div>
        <div class="value">{{ info.yzr || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">验证时间</div>
        <div class="value">{{ info.yzsj || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">验证状态</div>
        <div class="value">{{ info.yzzt || "-" }}</div>
      </div>
      <div class="line">
        <div class="label">处置预案</div>
        <div class="value">{{ info.czya || "-" }}</div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
.info {
  padding: 20px;
  box-sizing: border-box;
  .line {
    display: flex;
    align-items: center;
    .label {
      width: 200px;
      background: #f9fafb;
      border: 1px solid #eff0f1;
      padding: 8px 21px;
      box-sizing: border-box;
    }
    .value {
      width: calc(100% - 200px);
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      color: #667085;
      line-height: 20px;
      border: 1px solid #eff0f1;
      text-align: left;
      padding: 8px 21px;
      box-sizing: border-box;
    }
  }
}
::v-deep .el-dialog {
  border-radius: 12px;
  overflow: hidden;
  .el-dialog__body {
    padding: 0;
    max-height: 640px;
    overflow-y: scroll;
    padding-bottom: 20px;
    .el-form-item__label {
      padding: 0;
      line-height: 30px;
    }
    .el-form-item {
      margin-bottom: 10px;
    }
  }
}
</style>
