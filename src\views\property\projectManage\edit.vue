<template>
  <div class="container" v-loading="loading" element-loading-text="加载中...">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="flex-c">
        <h2 class="page-title">{{ isAddMode ? "新增项目" : "编辑项目" }}</h2>
      </div>
    </div>

    <!-- 表单内容 -->
    <el-form
      ref="myForm"
      :model="myForm"
      :rules="formRules"
      label-width="80px"
      label-position="top"
      inline
      class="edit-form"
    >
      <div class="card">
        <div class="cardTitle">基本信息</div>
        <div class="itemList flex-c">
          <el-form-item label="项目编号" prop="projectNo" class="item_grid_3">
            <el-input
              v-model="myForm.projectNo"
              size="small"
              disabled
              clearable
              placeholder="请输入项目编号"
            ></el-input>
          </el-form-item>
          <el-form-item label="项目名称" prop="projectName" class="item_grid_3">
            <el-input
              v-model="myForm.projectName"
              size="small"
              clearable
              placeholder="请输入项目名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="项目类型" prop="projectType" class="item_grid_3">
            <el-select
              v-model="myForm.projectType"
              clearable
              size="small"
              style="width: 100%"
              placeholder="请选择项目类型"
            >
              <el-option
                v-for="(item, i) in projectTypeOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="项目介绍"
            prop="projectDescription"
            class="item_grid_3"
          >
            <el-input
              v-model="myForm.projectDescription"
              size="small"
              clearable
              placeholder="请输入项目介绍"
            ></el-input>
          </el-form-item>
          <el-form-item label="属地" prop="sd" class="item_grid_3">
            <el-select
              v-model="myForm.sd"
              placeholder="请选择属地"
              clearable
              size="small"
              style="width: 100%"
            >
              <el-option
                v-for="item in sdOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="立项时间" class="item_grid_3">
            <el-date-picker
              v-model="myForm.projectEstablishTime"
              type="date"
              size="small"
              placeholder="日期选择"
              value-format="yyyy-MM-dd"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
          <el-form-item
            label="项目状态"
            prop="projectStatus"
            class="item_grid_3"
          >
            <el-select
              v-model="myForm.projectStatus"
              clearable
              size="small"
              style="width: 100%"
              placeholder="请选择项目状态"
            >
              <el-option
                v-for="(item, i) in projectStatusOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="建设单位"
            prop="constructionUnitId"
            class="item_grid_3"
          >
            <treeselect
              style="width: 100%; height: 32px"
              v-model="myForm.constructionUnitId"
              :options="enabledDeptOptions"
              :show-count="true"
              placeholder="请选择建设单位"
            />
          </el-form-item>
          <el-form-item label="负责人" prop="fzr" class="item_grid_3">
            <el-select
              v-model="myForm.fzr"
              clearable
              filterable
              remote
              size="small"
              style="width: 100%"
              placeholder="搜索选择负责人"
              :remote-method="searchUserList"
              :loading="userLoading"
            >
              <el-option
                v-for="item in userOptions"
                :key="item.userId"
                :label="`${item.userName || item.nickName || '未知用户'}`"
                :value="item.userId"
              >
                <span style="float: left">{{
                  item.userName || "未设置用户名"
                }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{
                  item.nickName || "未设置昵称"
                }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="联系方式" prop="lxfs" class="item_grid_3">
            <el-input
              v-model="myForm.lxfs"
              size="small"
              clearable
              placeholder="请输入联系方式"
            ></el-input>
          </el-form-item>
          <el-form-item label="运维人员" class="item_grid_3">
            <el-select
              v-model="myForm.ywryList"
              clearable
              filterable
              remote
              reserve-keyword
              multiple
              size="small"
              style="width: 100%"
              placeholder="输入以检索运维人员"
              :remote-method="searchYwryList"
              :loading="ywryLoading"
            >
              <el-option
                v-for="item in ywryOptions"
                :key="item.userId"
                :label="`${item.userName || item.nickName || '未知用户'}`"
                :value="item.userId"
              >
                <span style="float: left">{{
                  item.userName || "未设置用户名"
                }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{
                  item.nickName || "未设置昵称"
                }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="计划上线时间" class="item_grid_3">
            <el-date-picker
              v-model="myForm.planOnlineTime"
              type="date"
              size="small"
              placeholder="日期选择"
              value-format="yyyy-MM-dd"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="计划验收时间" class="item_grid_3">
            <el-date-picker
              v-model="myForm.planAcceptanceTime"
              type="date"
              size="small"
              placeholder="日期选择"
              value-format="yyyy-MM-dd"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
        </div>
      </div>

      <div class="card">
        <div class="cardTitle">厂商信息</div>
        <div class="itemList flex-c">
          <el-form-item label="开发厂商" class="item_grid_3">
            <el-select
              v-model="myForm.csDeveloperId"
              clearable
              filterable
              remote
              :remote-method="searchDeveloperVendors"
              :loading="developerVendorLoading"
              size="small"
              style="width: 100%"
              placeholder="需先在供应商管理页面中维护信息，再在该位置下拉检索供应商"
            >
              <el-option
                v-for="item in developerVendorOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="运维厂商" class="item_grid_3">
            <el-select
              v-model="myForm.csOperatorId"
              clearable
              filterable
              remote
              :remote-method="searchOperatorVendors"
              :loading="operatorVendorLoading"
              size="small"
              style="width: 100%"
              placeholder="需先在供应商管理页面中维护信息，再在该位置下拉检索供应商"
            >
              <el-option
                v-for="item in operatorVendorOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="安全厂商" class="item_grid_3">
            <el-select
              v-model="myForm.csSecurityVendorId"
              clearable
              filterable
              remote
              :remote-method="searchSecurityVendors"
              :loading="securityVendorLoading"
              size="small"
              style="width: 100%"
              placeholder="需先在供应商管理页面中维护信息，再在该位置下拉检索供应商"
            >
              <el-option
                v-for="item in securityVendorOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="开发厂商联系人" class="item_grid_3">
            <el-select
              v-model="myForm.csDeveloperUserId"
              clearable
              size="small"
              style="width: 100%"
              placeholder="选择该厂商人员"
            >
              <el-option
                v-for="item in developerContactOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="运维厂商联系人" class="item_grid_3">
            <el-select
              v-model="myForm.csOperatorContactId"
              clearable
              size="small"
              style="width: 100%"
              placeholder="输入"
            >
              <el-option
                v-for="item in operatorContactOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="安全厂商联系人" class="item_grid_3">
            <el-select
              v-model="myForm.csSecurityVendorContactId"
              clearable
              size="small"
              style="width: 100%"
              placeholder="输入"
            >
              <el-option
                v-for="item in securityContactOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="开发厂商联系电话" class="item_grid_3">
            <el-input
              v-model="myForm.developerContactPhone"
              size="small"
              clearable
              placeholder="自动带出"
              :disabled="true"
            ></el-input>
          </el-form-item>
          <el-form-item label="运维厂商联系电话" class="item_grid_3">
            <el-input
              v-model="myForm.operatorContactPhone"
              size="small"
              clearable
              placeholder="自动带出"
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item label="安全厂商联系电话" class="item_grid_3">
            <el-input
              v-model="myForm.securityContactPhone"
              size="small"
              clearable
              placeholder="自动带出"
              disabled
            ></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="card">
        <div class="cardTitle">项目文件</div>
        <div class="itemList">
          <div class="all_table_title">
            <file-upload
              @input="uploadChange($event)"
              btnName="上传文件"
              :isShowTip="false"
              ref="uploadRefs"
            />
          </div>
          <my-table :tableData="tableData" :showDelete='true' @handleDelete='deleteFile'> </my-table>
        </div>
      </div>
    </el-form>

    <!-- 底部操作栏 -->
    <div class="footer-actions">
      <el-button type="primary" @click="handleSave">保存</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
  </div>
</template>

<script>
import { listSupplier } from "@/api/property/supplierManage";
import {
  getProject,
  addProject,
  updateProject,
} from "@/api/property/projectManage";
import {
  listMaintenancePersonnel,
  listContactsBySupplier,
} from "@/api/property/maintenancePersonnel";
import { listUser } from "@/api/system/user";
import { listSd } from "@/api/property/projectManage";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { deptTreeSelect } from "@/api/serve/orderlist";
import MyTable from "@/views/monitorCenter/application/components/projectTable.vue";
import FileUpload from "@/components/FileUpload/index.vue";

export default {
  name: "projectManageEdit",
  components: {
    Treeselect,
    MyTable,
    FileUpload,
  },
  dicts: [],
  data() {
    return {
      myForm: {
        projectNo: "",
        projectName: "",
        projectType: "",
        projectDescription: "",
        sd: "",
        projectEstablishTime: "",
        projectStatus: "",
        constructionUnitId: undefined,
        fzr: "",
        lxfs: "",
        ywryList: [],
        planAcceptanceTime: "",
        planOnlineTime: "",
        fileList: [],
        // 厂商信息字段
        csDeveloperId: "", // 开发厂商id
        csDeveloperUserId: "", // 开发厂商联系人id
        developerContactPhone: "", // 开发厂商联系电话
        csOperatorId: "", // 运维厂商id
        csOperatorContactId: "", // 运维厂商联系人id
        operatorContactPhone: "", // 运维厂商联系电话
        csSecurityVendorId: "", // 安全厂商id
        csSecurityVendorContactId: "", // 安全厂商联系人id
        securityContactPhone: "", // 安全厂商联系电话
      },
      // 项目类型
      projectTypeOptions: [
        { label: "软件", value: "软件" },
        { label: "硬件", value: "硬件" },
        { label: "其他", value: "其他" },
      ],
      //项目状态
      projectStatusOptions: [
        { label: "开发中", value: "开发中" },
        { label: "试运行", value: "试运行" },
        { label: "运行中", value: "运行中" },
        { label: "已下线", value: "已下线" },
      ],
      //属地
      sdOptions: [],
      //建设单位
      enabledDeptOptions: [],
      // 运维人员相关数据
      ywryOptions: [],
      ywryLoading: false,
      // 用户管理员相关数据
      userOptions: [],
      userLoading: false,
      tableData: [],
      // 厂商相关数据
      developerVendorOptions: [],
      developerVendorLoading: false,
      developerContactOptions: [],
      operatorVendorOptions: [],
      operatorVendorLoading: false,
      operatorContactOptions: [],
      securityVendorOptions: [],
      securityVendorLoading: false,
      securityContactOptions: [],
      // 页面加载状态
      loading: false,
      // 页面模式：true为新增模式，false为编辑模式
      isAddMode: false,
      // 表单验证规则
      formRules: {
        projectName: [
          { required: true, message: "请输入项目名称", trigger: "blur" },
        ],
        projectType: [
          { required: true, message: "请选择项目类型", trigger: "change" },
        ],
        sd: [{ required: true, message: "请选择属地", trigger: "change" }],
        projectStatus: [
          { required: true, message: "请选择项目状态", trigger: "change" },
        ],
        constructionUnitId: [
          { required: true, message: "请选择建设单位", trigger: "change" },
        ],
        fzr: [{ required: true, message: "请输入负责人", trigger: "blur" }],
        lxfs: [{ required: true, message: "请输入联系方式", trigger: "blur" }],
      },
    };
  },
  watch: {
    // 监听开发厂商选择变化
    "myForm.csDeveloperId"(newVal) {
      if (newVal) {
        // 调用API获取该厂商的联系人列表
        this.loadDeveloperContacts(newVal);
      } else {
        this.developerContactOptions = [];
        this.myForm.csDeveloperUserId = "";
        this.myForm.developerContactPhone = "";
      }
    },
    // 监听开发厂商联系人选择变化
    "myForm.csDeveloperUserId"(newVal) {
      if (newVal) {
        const contact = this.developerContactOptions.find(
          (item) => item.id === newVal
        );
        this.myForm.developerContactPhone = contact ? contact.phone : "";
      } else {
        this.myForm.developerContactPhone = "";
      }
    },
    // 监听运维厂商选择变化
    "myForm.csOperatorId"(newVal) {
      if (newVal) {
        // 调用API获取该厂商的联系人列表
        this.loadOperatorContacts(newVal);
      } else {
        this.operatorContactOptions = [];
        this.myForm.csOperatorContactId = "";
        this.myForm.operatorContactPhone = "";
      }
    },
    // 监听运维厂商联系人选择变化
    "myForm.csOperatorContactId"(newVal) {
      if (newVal) {
        const contact = this.operatorContactOptions.find(
          (item) => item.id === newVal
        );
        this.myForm.operatorContactPhone = contact ? contact.phone : "";
      } else {
        this.myForm.operatorContactPhone = "";
      }
    },
    // 监听安全厂商选择变化
    "myForm.csSecurityVendorId"(newVal) {
      if (newVal) {
        // 调用API获取该厂商的联系人列表
        this.loadSecurityContacts(newVal);
      } else {
        this.securityContactOptions = [];
        this.myForm.csSecurityVendorContactId = "";
        this.myForm.securityContactPhone = "";
      }
    },
    // 监听安全厂商联系人选择变化
    "myForm.csSecurityVendorContactId"(newVal) {
      if (newVal) {
        const contact = this.securityContactOptions.find(
          (item) => item.id === newVal
        );
        this.myForm.securityContactPhone = contact ? contact.phone : "";
      } else {
        this.myForm.securityContactPhone = "";
      }
    },
  },
  created() {
    this.initData();
    this.getYwryList(); // 初始化运维人员列表
    this.getUserList(); // 初始化用户列表
  },
  methods: {
    // 初始化数据
    initData() {
      /** 查询部门下拉树结构 */
      deptTreeSelect().then((response) => {
        this.enabledDeptOptions = this.filterDisabledDept(
          JSON.parse(JSON.stringify(response.data))
        );
      });
      listSd().then((res) => {
        this.sdOptions = res.data.map((item) => {
          return {
            label: item,
            value: item,
          };
        });
      });
      // 判断是新增还是编辑模式
      const mode = this.$route.query.mode;
      const id = this.$route.query.id;

      if (mode === "add") {
        // 新增模式
        this.isAddMode = true;
        console.log("新增模式：初始化空表单");
        this.initFormForAdd();
      } else if (id) {
        // 编辑模式
        this.isAddMode = false;
        console.log("编辑模式：加载应用数据，ID:", id);
        this.loadApplicationData(id);
      } else {
        // 默认为新增模式
        this.isAddMode = true;
        console.log("默认新增模式：初始化空表单");
        this.initFormForAdd();
      }
    },
    uploadChange(val) {
      let query = {
        filePath: val || "",
        fileName: val ? val.match(/\/([^\/]+)$/)[1] : "",
      };
      console.log("上传文件：", query);
      this.$refs.uploadRefs.fileList = []
      let fileList= this.myForm.fileList;
      this.myForm.fileList = [...fileList, query];
      this.tableData = this.myForm.fileList
    },
    deleteFile(index,row){
      let fileList= this.myForm.fileList;
      fileList.splice(index,1)
      this.myForm.fileList = fileList
      this.tableData = this.myForm.fileList
    },
    // 过滤禁用的部门
    filterDisabledDept(deptList) {
      return deptList.filter((dept) => {
        if (dept.disabled) {
          return false;
        }
        if (dept.children && dept.children.length) {
          dept.children = this.filterDisabledDept(dept.children);
        }
        return true;
      });
    },

    // 初始化新增表单
    initFormForAdd() {
      // 重置表单为默认值
      this.myForm = {
        projectNo: "",
        projectName: "",
        projectType: "",
        projectDescription: "",
        sd: "",
        projectEstablishTime: "",
        projectStatus: "",
        constructionUnitId: undefined,
        fzr: "",
        lxfs: "",
        ywryList: [],
        planAcceptanceTime: "",
        planOnlineTime: "",
        fileList: [],
        // 厂商信息
        csDeveloperId: "",
        csDeveloperUserId: "",
        csOperatorId: "",
        csOperatorContactId: "",
        csSecurityVendorId: "",
        csSecurityVendorContactId: "",
      };

      console.log("新增表单初始化完成:", this.myForm);
    },

    // 加载应用数据
    async loadApplicationData(id) {
      try {
        this.loading = true;
        console.log("Loading application data for ID:", id);

        const response = await getProject(id);
        if (response.code === 200 && response.data) {
          const data = response.data;

          // 映射API返回的数据到表单
          this.myForm = {
            ...this.myForm,
            // 基本信息
            id: data.id,
            projectNo: data.projectNo || "",
            projectName: data.projectName || "",
            projectType: data.projectType || "",
            projectDescription: data.projectDescription || "",
            sd: data.sd || "",
            projectEstablishTime: data.projectEstablishTime || "",
            projectStatus: data.projectStatus || "",
            constructionUnitId: data.constructionUnitId || undefined,
            fzr: data.fzr || "",
            lxfs: data.lxfs || "",
            planAcceptanceTime: data.planAcceptanceTime || "",
            planOnlineTime: data.planOnlineTime || "",
            fileList: data.fileList || "",
            // 运维人员处理 - 从数组中提取所有人员的ID
            ywryList:
              data.ywryList && data.ywryList.length > 0
                ? data.ywryList.map((item) => item.ywryId)
                : [],

            // 厂商信息
            csDeveloperId: data.csDeveloperId || "",
            csDeveloperUserId: data.csDeveloperUserId || "",
            csOperatorId: data.csOperatorId || "",
            csOperatorContactId: data.csOperatorContactId || "",
            csSecurityVendorId: data.csSecurityVendorId || "",
            csSecurityVendorContactId: data.csSecurityVendorContactId || "",
          };

          this.tableData = this.myForm.fileList

          // 预加载厂商选项以确保正确回显
          console.log("准备预加载厂商选项...");
          await this.preloadVendorOptions();
          console.log("厂商选项预加载完成");

          // 加载厂商联系人信息
          if (this.myForm.csDeveloperId) {
            this.loadDeveloperContacts(this.myForm.csDeveloperId);
          }
          if (this.myForm.csOperatorId) {
            this.loadOperatorContacts(this.myForm.csOperatorId);
          }
          if (this.myForm.csSecurityVendorId) {
            this.loadSecurityContacts(this.myForm.csSecurityVendorId);
          }
        } else {
          this.$message.error(response.msg || "获取应用详情失败");
        }
      } catch (error) {
        console.error("Error loading application data:", error);
        this.$message.error("获取应用详情失败");
      } finally {
        this.loading = false;
      }
    },

    // 保存数据
    async handleSave() {
      this.$refs.myForm.validate(async (valid) => {
        if (valid) {
          try {
            this.loading = true;
            // 准备保存的数据，确保字段名与API一致
            const saveData = {
              ...this.myForm,
              // 辅运维人员字段映射 - 将多个ID转换为数组格式
              ywryList:
                this.myForm.ywryList && this.myForm.ywryList.length > 0
                  ? this.myForm.ywryList.map((id) => ({ ywryId: id }))
                  : [],
            };

            // 如果是新增操作（没有id），则移除id字段
            if (!this.myForm.id) {
              delete saveData.id;
            }

            let response;
            if (this.isAddMode) {
              // 新增模式：调用新增接口
              response = await addProject(saveData);
            } else {
              // 编辑模式：调用编辑接口
              response = await updateProject(saveData);
            }

            console.log("API response:", response);

            if (response.code === 200) {
              this.$message.success(this.isAddMode ? "新增成功" : "保存成功");
              this.goBack();
            } else {
              console.error("API error:", response);
              this.$message.error(
                response.msg || (this.isAddMode ? "新增失败" : "保存失败")
              );
            }
          } catch (error) {
            console.error("Error saving application data:", error);
            this.$message.error("保存失败");
          } finally {
            this.loading = false;
          }
        } else {
          this.$message.error("请检查表单数据");
        }
      });
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 搜索开发厂商
    async searchDeveloperVendors(query) {
      if (query !== "") {
        this.developerVendorLoading = true;
        try {
          const response = await listSupplier({
            pageNum: 1,
            pageSize: 1000,
            gysName: query, // 按供应商名称模糊查询
          });
          if (response.code === 200 && response.data && response.data.list) {
            this.developerVendorOptions = response.data.list.map((item) => ({
              id: String(item.id), // 确保ID为字符串类型
              name: item.gysName,
              code: item.tyxydm, // 统一社会信用代码
            }));
          } else {
            this.developerVendorOptions = [];
          }
        } catch (error) {
          console.error("搜索开发厂商失败:", error);
          this.developerVendorOptions = [];
        } finally {
          this.developerVendorLoading = false;
        }
      } else {
        this.developerVendorOptions = [];
      }
    },

    // 搜索运维厂商
    async searchOperatorVendors(query) {
      if (query !== "") {
        this.operatorVendorLoading = true;
        try {
          const response = await listSupplier({
            pageNum: 1,
            pageSize: 1000,
            gysName: query, // 按供应商名称模糊查询
          });
          if (response.code === 200 && response.data && response.data.list) {
            this.operatorVendorOptions = response.data.list.map((item) => ({
              id: String(item.id), // 确保ID为字符串类型
              name: item.gysName,
              code: item.tyxydm, // 统一社会信用代码
            }));
          } else {
            this.operatorVendorOptions = [];
          }
        } catch (error) {
          console.error("搜索运维厂商失败:", error);
          this.operatorVendorOptions = [];
        } finally {
          this.operatorVendorLoading = false;
        }
      } else {
        this.operatorVendorOptions = [];
      }
    },

    // 搜索安全厂商
    async searchSecurityVendors(query) {
      if (query !== "") {
        this.securityVendorLoading = true;
        try {
          const response = await listSupplier({
            pageNum: 1,
            pageSize: 1000,
            gysName: query, // 按供应商名称模糊查询
          });
          if (response.code === 200 && response.data && response.data.list) {
            this.securityVendorOptions = response.data.list.map((item) => ({
              id: String(item.id), // 确保ID为字符串类型
              name: item.gysName,
              code: item.tyxydm, // 统一社会信用代码
            }));
          } else {
            this.securityVendorOptions = [];
          }
        } catch (error) {
          console.error("搜索安全厂商失败:", error);
          this.securityVendorOptions = [];
        } finally {
          this.securityVendorLoading = false;
        }
      } else {
        this.securityVendorOptions = [];
      }
    },

    // 加载开发厂商联系人
    async loadDeveloperContacts(supplierId) {
      try {
        console.log("Loading developer contacts for supplier:", supplierId);
        const response = await listContactsBySupplier(supplierId);
        if (response.code === 200 && response.data) {
          // listNotPage接口直接返回数组数据
          const contactList = Array.isArray(response.data) ? response.data : [];
          this.developerContactOptions = contactList.map((contact) => ({
            id: String(contact.id), // 确保ID为字符串类型
            name: contact.userName || contact.nickName || "未知联系人",
            phone: contact.phone || "",
          }));
          console.log(
            "Developer contacts loaded:",
            this.developerContactOptions
          );

          // 如果当前有选中的联系人，自动填充联系电话
          if (this.myForm.csDeveloperUserId) {
            const selectedContact = this.developerContactOptions.find(
              (contact) => contact.id === this.myForm.csDeveloperUserId
            );
            if (selectedContact) {
              this.myForm.developerContactPhone = selectedContact.phone || "";
              console.log(
                "自动填充开发厂商联系电话:",
                this.myForm.developerContactPhone
              );
            }
          }
        } else {
          this.developerContactOptions = [];
          console.warn("Failed to load developer contacts:", response.msg);
        }
      } catch (error) {
        console.error("Error loading developer contacts:", error);
        this.developerContactOptions = [];
      }
    },

    // 加载运维厂商联系人
    async loadOperatorContacts(supplierId) {
      try {
        console.log("Loading operator contacts for supplier:", supplierId);
        const response = await listContactsBySupplier(supplierId);
        if (response.code === 200 && response.data) {
          // listNotPage接口直接返回数组数据
          const contactList = Array.isArray(response.data) ? response.data : [];
          this.operatorContactOptions = contactList.map((contact) => ({
            id: String(contact.id), // 确保ID为字符串类型
            name: contact.userName || contact.nickName || "未知联系人",
            phone: contact.phone || "",
          }));
          console.log("Operator contacts loaded:", this.operatorContactOptions);

          // 如果当前有选中的联系人，自动填充联系电话
          if (this.myForm.csOperatorContactId) {
            const selectedContact = this.operatorContactOptions.find(
              (contact) => contact.id === this.myForm.csOperatorContactId
            );
            if (selectedContact) {
              this.myForm.operatorContactPhone = selectedContact.phone || "";
              console.log(
                "自动填充运维厂商联系电话:",
                this.myForm.operatorContactPhone
              );
            }
          }
        } else {
          this.operatorContactOptions = [];
          console.warn("Failed to load operator contacts:", response.msg);
        }
      } catch (error) {
        console.error("Error loading operator contacts:", error);
        this.operatorContactOptions = [];
      }
    },

    // 加载安全厂商联系人
    async loadSecurityContacts(supplierId) {
      try {
        console.log("Loading security contacts for supplier:", supplierId);
        const response = await listContactsBySupplier(supplierId);
        if (response.code === 200 && response.data) {
          // listNotPage接口直接返回数组数据
          const contactList = Array.isArray(response.data) ? response.data : [];
          this.securityContactOptions = contactList.map((contact) => ({
            id: String(contact.id), // 确保ID为字符串类型
            name: contact.userName || contact.nickName || "未知联系人",
            phone: contact.phone || "",
          }));
          console.log("Security contacts loaded:", this.securityContactOptions);

          // 如果当前有选中的联系人，自动填充联系电话
          if (this.myForm.csSecurityVendorContactId) {
            const selectedContact = this.securityContactOptions.find(
              (contact) => contact.id === this.myForm.csSecurityVendorContactId
            );
            if (selectedContact) {
              this.myForm.securityContactPhone = selectedContact.phone || "";
              console.log(
                "自动填充安全厂商联系电话:",
                this.myForm.securityContactPhone
              );
            }
          }
        } else {
          this.securityContactOptions = [];
          console.warn("Failed to load security contacts:", response.msg);
        }
      } catch (error) {
        console.error("Error loading security contacts:", error);
        this.securityContactOptions = [];
      }
    },

    // 预加载厂商选项以确保正确回显
    async preloadVendorOptions() {
      try {
        // 收集需要预加载的厂商ID
        const vendorIds = [];
        if (this.myForm.csDeveloperId)
          vendorIds.push(this.myForm.csDeveloperId);
        if (this.myForm.csOperatorId) vendorIds.push(this.myForm.csOperatorId);
        if (this.myForm.csSecurityVendorId)
          vendorIds.push(this.myForm.csSecurityVendorId);

        if (vendorIds.length === 0) {
          return;
        }

        const response = await listSupplier({
          pageNum: 1,
          pageSize: 1000,
        });

        console.log("厂商列表API响应:", response);

        if (response.code === 200 && response.data && response.data.list) {
          const allVendors = response.data.list;

          // 为每个厂商类型设置对应的选项
          if (this.myForm.csDeveloperId) {
            const developerVendor = allVendors.find(
              (v) => v.id == this.myForm.csDeveloperId
            ); // 使用 == 而不是 === 以处理类型转换
            if (developerVendor) {
              this.developerVendorOptions = [
                {
                  id: String(developerVendor.id), // 确保ID为字符串类型
                  name: developerVendor.gysName,
                  code: developerVendor.tyxydm, // 统一社会信用代码
                },
              ];
            }
          }

          if (this.myForm.csOperatorId) {
            const operatorVendor = allVendors.find(
              (v) => v.id == this.myForm.csOperatorId
            ); // 使用 == 而不是 === 以处理类型转换
            if (operatorVendor) {
              this.operatorVendorOptions = [
                {
                  id: String(operatorVendor.id), // 确保ID为字符串类型
                  name: operatorVendor.gysName,
                  code: operatorVendor.tyxydm, // 统一社会信用代码
                },
              ];
            }
          }

          if (this.myForm.csSecurityVendorId) {
            const securityVendor = allVendors.find(
              (v) => v.id == this.myForm.csSecurityVendorId
            ); // 使用 == 而不是 === 以处理类型转换
            if (securityVendor) {
              this.securityVendorOptions = [
                {
                  id: String(securityVendor.id), // 确保ID为字符串类型
                  name: securityVendor.gysName,
                  code: securityVendor.tyxydm, // 统一社会信用代码
                },
              ];
            }
          }
          // 强制触发Vue响应式更新
          this.$forceUpdate();
        } else {
          console.error("厂商列表API响应异常:", response);
        }
      } catch (error) {
        console.error("预加载厂商选项异常:", error);
      }
    },
    // 搜索运维人员
    searchYwryList(query) {
      this.ywryLoading = true;
      this.getYwryList(query);
    },

    // 获取运维人员列表
    async getYwryList(keyword = "") {
      try {
        const params = {
          pageNum: 1,
          pageSize: 1000, // 增加页面大小以获取更多数据
          status: 1, // 只查询正常状态的人员
        };

        // 如果有关键词，同时按userName和nickName搜索
        if (keyword && keyword.trim()) {
          const trimmedKeyword = keyword.trim();
          // 先尝试按userName搜索
          params.userName = trimmedKeyword;
        }
        const response = await listMaintenancePersonnel(params);
        if (response.code === 200 && response.data && response.data.list) {
          let list = response.data.list;

          // 如果按userName搜索没有结果，再尝试按nickName搜索
          if (keyword && keyword.trim() && list.length === 0) {
            const nickNameParams = {
              pageNum: 1,
              pageSize: 1000,
              status: 1,
              nickName: keyword.trim(),
            };
            const nickNameResponse = await listMaintenancePersonnel(
              nickNameParams
            );
            if (
              nickNameResponse.code === 0 &&
              nickNameResponse.data &&
              nickNameResponse.data.list
            ) {
              list = nickNameResponse.data.list;
            }
          }

          this.ywryOptions = list;
        } else {
          this.ywryOptions = [];
        }
      } catch (error) {
        console.error("获取运维人员列表异常:", error);
        this.ywryOptions = [];
        this.$message.error("获取运维人员列表失败");
      } finally {
        this.ywryLoading = false;
      }
    },
        // 搜索用户列表
    searchUserList(query) {
      this.userLoading = true;
      this.getUserList(query);
    },

    // 获取用户列表
    async getUserList(keyword = "") {
      try {
        const params = {
          pageNum: 1,
          pageSize: 1000,
          status: "0", // 正常状态
        };

        // 如果有关键词，添加用户名搜索
        if (keyword && keyword.trim()) {
          params.userName = keyword.trim();
        }

        const response = await listUser(params);

        if (response.code === 200 && response.rows) {
          this.userOptions = response.rows;
        } else {
          this.userOptions = [];
        }
      } catch (error) {
        console.error("获取用户列表异常:", error);
        this.userOptions = [];
        this.$message.error("获取用户列表失败");
      } finally {
        this.userLoading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 120px; // 增加底部内边距，为固定的操作栏留出空间
  background-color: #f5f5f5;
  min-height: 100vh;
  overflow-y: auto; // 确保可以滚动
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: #fff;
  border-radius: 15px;
  margin-bottom: 12px;

  .back-btn {
    font-size: 16px;
    color: #0057fe;
    margin-right: 12px;

    &:hover {
      color: #003db8;
    }
  }

  .page-title {
    margin: 0;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 20px;
    color: #1d2129;
    line-height: 28px;
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.edit-form {
  .card {
    border-radius: 15px;
    padding: 20px 20px;
    box-sizing: border-box;
    background-color: #fff;
    height: auto;
    margin-bottom: 12px;
    overflow: visible; // 确保卡片内容可见

    .cardTitle {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 700;
      font-size: 18px;
      color: #1d2129;
      line-height: 24px;
      text-align: left;
      margin-bottom: 10px;
      padding-left: 18px;
      box-sizing: border-box;
      position: relative;

      &::before {
        content: "";
        position: absolute;
        top: 2px;
        left: 0;
        width: 10px;
        height: 20px;
        background: url("~@/assets/images/cardTitle_icon.png");
        background-size: 100% 100%;
      }
    }

    .lineTitle {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 500;
      font-size: 16px;
      color: #1d2129;
      line-height: 24px;
      text-align: left;
      margin: 12px 0 0 32px;
    }
  }
}

.itemList {
  flex-wrap: wrap;
  padding: 0 20px;
  box-sizing: border-box;

  .item_grid_3 {
    width: 30%;
    margin-left: 16px;
    margin-bottom: 20px;
  }
}

.footer-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1px solid #e5e6eb;
  padding: 20px;
  display: flex;
  justify-content: center;
  gap: 12px;
  z-index: 1000;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1); // 添加阴影效果
}

// 全局样式覆盖
::v-deep .el-form-item__label {
  padding: 0;
  line-height: 30px;
}

::v-deep .el-form-item {
  margin-bottom: 10px;
}

::v-deep .el-table {
  border-radius: 8px;
  overflow: hidden;
}

::v-deep .el-table th {
  background-color: #f8f9fa;
}

// 确保页面可以正常滚动
body {
  overflow-y: auto !important;
}

// 表格容器样式
::v-deep .el-table__body-wrapper {
  overflow-x: auto;
}

// 确保表格内的输入框和选择器正常显示
::v-deep .el-table .el-input,
::v-deep .el-table .el-select {
  width: 100%;
}
.all_table_title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  margin: 15px 0 10px;
}
</style>
