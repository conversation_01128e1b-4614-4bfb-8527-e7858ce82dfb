<template>
  <div class="wrap flex-b">
    <div class="aqItem flex-b" v-for="(item, i) in aqlist" :key="i">
      <img :src="item.icon" class="icon" />
      <div class="info">
        <div class="name">{{ item.name }}</div>
        <div class="flex-b">
          <div class="num">
            最近7天：
            <span class="blue">
              {{ item.num == 0 ? 0 : item.num ? item.num : "-" }}
            </span>
            起
          </div>
          <div class="history">查看历史</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    aqlist: {
      type: Array,
      default: () => [],
    },
  },
};
</script>

<style lang="scss" scoped>
.aqItem {
  padding: 12px;
  box-sizing: border-box;
  flex: 1;
  margin-right: 40px;
  .icon {
    width: 82px;
    height: 82px;
    margin-right: 16px;
  }
  .info {
    width: 100%;
    height: 72px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .name {
      font-family: Source <PERSON>, Source <PERSON>;
      font-weight: 500;
      font-size: 18px;
      color: #4e5969;
      line-height: 24px;
      text-align: left;
    }
    .num {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #4e5969;
      line-height: 24px;
      text-align: left;
      .blue {
        color: #0057fe;
      }
    }
    .history {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #667085;
      line-height: 24px;
      text-align: left;
      cursor: pointer;
    }
  }
}
</style>
