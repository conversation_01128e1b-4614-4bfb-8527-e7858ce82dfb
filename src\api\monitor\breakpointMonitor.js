import request from '@/utils/request'

// 查询断点监测日志列表
export function listBreakpointMonitor(query) {
  return request({
    url: '/tyywpt/tTyywJcLog/list',
    method: 'get',
    params: query
  })
}

// 获取断点监测日志详细信息
export function getBreakpointMonitor(id) {
  return request({
    url: '/tyywpt/tWyywJcLog/getInfo',
    method: 'get',
    params: { id: id }
  })
}

// 新增断点监测日志
export function addBreakpointMonitor(data) {
  return request({
    url: '/tyywpt/tWyywJcLog/add',
    method: 'post',
    data: data
  })
}

// 修改断点监测日志
export function updateBreakpointMonitor(data) {
  return request({
    url: '/tyywpt/tWyywJcLog/edit',
    method: 'put',
    data: data
  })
}

// 删除断点监测日志
export function delBreakpointMonitor(ids) {
  return request({
    url: '/tyywpt/tWyywJcLog/remove',
    method: 'delete',
    params: { ids: ids }
  })
}

// 导出断点监测日志列表
export function exportBreakpointMonitor(query) {
  return request({
    url: '/tyywpt/tWyywJcLog/export',
    method: 'post',
    params: query
  })
}
