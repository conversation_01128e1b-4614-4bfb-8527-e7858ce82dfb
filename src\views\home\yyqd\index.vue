<template>
  <div style="height: 200px; overflow-y: auto">
    <el-table :data="tableData" style="width: 100%">
      <el-table-column
        prop="name"
        label="应用名称"
        align="center"
        :show-overflow-tooltip="true"
        min-width="180"
      >
      </el-table-column>
      <el-table-column label="系统状态" align="center"
        ><template slot-scope="scope">
          <div v-if="scope.row.xtStatus == 1">谋划中</div>
          <div v-if="scope.row.xtStatus == 2">建设中</div>
          <div v-if="scope.row.xtStatus == 3">试运行</div>
          <div v-if="scope.row.xtStatus == 4">运行中</div>
          <div v-if="scope.row.xtStatus == 5">停用</div>
        </template>
      </el-table-column>
      <el-table-column prop="deptName" label="所属部门" align="center">
      </el-table-column>
      <el-table-column prop="adminName" label="应用管理员" align="center">
      </el-table-column>
      <el-table-column prop="ywjzsj" label="运维截止时间" align="center">
      </el-table-column>
      <el-table-column label="应用状态" align="center">
        <template slot-scope="scope">
          <div>{{ scope.row.status == 1 ? "正常" : "异常" }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="ywcs" label="运维厂商" align="center">
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button @click="jumpUrl(scope.row)" type="text"> 查看 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- <div style="display: flex; justify-content: flex-end; margin-top: 20px">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        layout="total, prev, pager, next"
        :total="total"
      ></el-pagination>
    </div> -->
    <div class="flex-c-c" style="margin-top: 24px">
      <div class="text_link" @click="goMore">查看更多</div>
    </div>
  </div>
</template>

<script>
import { listApplication } from "@/api/property/applicationManage";
export default {
  props: {
    name: {
      type: String,
      default: "",
    },
    deptId: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      tableData: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  mounted() {
    this.getList();
  },
  watch: {
    name(newVal, oldVal) {
      this.getList();
    },
    deptId(newVal, oldVal) {
      this.getList();
    },
  },
  methods: {
    // 分页 - 当前页变化
    handleCurrentChange(page) {
      this.queryParams.pageNum = page;
      this.getList();
    },
    // 获取应用列表
    async getList() {
      try {
        var params = {
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
          name: this.name,
          constructionUnitId: this.deptId,
        };
        const response = await listApplication(params);
        if (response.code === 200 && response.data) {
          this.tableData = response.data.list;
          this.total = response.data.total || 0;
        } else {
          this.tableData = [];
          this.total = 0;
          this.$message.error(response.msg || "获取应用列表失败");
        }
      } catch (error) {
        this.tableData = [];
        this.total = 0;
        this.$message.error("获取应用列表失败");
      } finally {
        this.loading = false;
      }
    },
    jumpUrl(item) {
      this.$router.push({
        path: "/monitorCenter/applicationDetail",
        query: { id: item.id },
      });
    },
    goMore() {
      this.$router.push({
        path: "/monitorCenter/application",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-table .el-table__cell {
  padding: 8px 0 !important;
}
</style>
