<template>
  <div style="width: 100%; height: 230px" id="zcslbh"></div>
</template>

<script>
import * as echarts from "echarts";
export default {
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  mounted() {},
  watch: {
    data: {
      handler(val) {
        this.initChart(val);
      },
      deep: true,
    },
  },
  methods: {
    initChart(data) {
      let chart = echarts.init(document.getElementById("zcslbh"));
      let option = {
        color: ["#37E2E2", "#722ED1"],
        tooltip: {
          trigger: "axis",
        },
        grid: {
          left: "2%",
          right: 0,
          bottom: "10%",
          top: "20%",
          containLabel: true,
        },
        legend: {
          icon: "roundRect",
          itemWidth: 16,
          itemHeight: 10,
          itemGap: 20,
          right: 0,
        },
        xAxis: {
          type: "category",
          data: data.map((x) => x.name),
          boundaryGap: true,
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#858D9D",
          },
          axisLine: {
            lineStyle: {
              color: "#DEE3E9",
            },
          },
        },
        yAxis: {
          type: "value",
          name: "单位：个",
          splitNumber: 4,
          axisLabel: {
            color: "#667085",
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: "#DEE3E9",
              type: "dotted",
            },
          },
        },
        series: [
          {
            name: "应用",
            type: "line",
            smooth: false,
            showSymbol: false,
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#37E2E2",
                },
                {
                  offset: 1,
                  color: "#37E2E200",
                },
              ]),
            },
            emphasis: {
              focus: "series",
            },
            data: data.map((x) => x.yyNum),
          },
          {
            name: "单位",
            type: "line",
            smooth: false,
            showSymbol: false,
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#722ED1",
                },
                {
                  offset: 1,
                  color: "#722ED100",
                },
              ]),
            },
            data: data.map((x) => x.dwNum),
          },
        ],
      };
      chart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
