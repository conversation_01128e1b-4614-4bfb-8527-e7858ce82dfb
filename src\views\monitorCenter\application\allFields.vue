<template>
  <div class="wrap">
    <div class="li" v-for="(item, i) in list" :key="i">
      <div class="flex-b">
        <div class="flex-c">
          <img src="@/assets/images/monitor/data.png" class="icon" />
          <div class="name">{{ item.name }}</div>
          <div class="bh">{{ item.bh }}</div>
        </div>
        <div class="tags flex-c">
          <div class="tag_top flex-c-c" v-for="(x, j) in item.tag" :key="j">
            {{ x }}
          </div>
        </div>
      </div>
      <div class="line flex-c">
        <div class="lineItem">
          <div class="label">数据源单位</div>
          <div class="value">{{ item.sjydw }}</div>
        </div>
        <div class="lineItem">
          <div class="label">所属应用</div>
          <div class="value">{{ item.ssyy }}</div>
        </div>
        <div class="lineItem">
          <div class="label">领域分类</div>
          <div class="value">{{ item.lyfl }}</div>
        </div>
        <div class="lineItem">
          <div class="label">归集总类</div>
          <div class="value">{{ item.gjzl }}</div>
        </div>
        <div class="lineItem">
          <div class="label">时间范围</div>
          <div class="value">{{ item.sjfw }}</div>
        </div>
        <div class="lineItem">
          <div class="label">最后一次更新</div>
          <div class="value">{{ item.zhycgx }}</div>
        </div>
        <div class="lineItem">
          <div class="label">更新频率</div>
          <div class="value">{{ item.gxpl }}</div>
        </div>
      </div>
      <div class="flex-b">
        <div class="tagList flex-c">
          <div class="tag tag_blu">受限共享</div>
          <div class="tag tag_gre">受限开放</div>
          <div class="tag tag_org">(市→县)回流</div>
          <div class="tag">访问量：{{ item.fwl }}</div>
          <div class="tag">申请量：{{ item.sql }}</div>
        </div>
        <div>
          <el-button type="text" style="margin-right: 12px">
            数据说明书<i class="el-icon-arrow-right"></i>
          </el-button>
          <el-button type="text">
            查看详情<i class="el-icon-arrow-right"></i>
          </el-button>
        </div>
      </div>

      <div class="divider" v-if="i !== list.length"></div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
};
</script>

<style lang="scss" scoped>
.wrap {
  .li {
    margin: 20px;
    box-sizing: border-box;
    .icon {
      width: 14px;
      height: 16px;
    }
    .name {
      font-family: Roboto, Roboto;
      font-weight: 500;
      font-size: 16px;
      color: #000000;
      line-height: 24px;
      text-align: left;
      margin: 0 30px 0 6px;
    }
    .bh {
      font-family: Roboto, Roboto;
      font-weight: 400;
      font-size: 14px;
      color: #6b7280;
      line-height: 20px;
      text-align: left;
    }
    .tag_top {
      background: #eff6ff;
      border-radius: 4px 4px 4px 4px;
      padding: 2px 8px;
      box-sizing: border-box;
      margin-left: 16px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      color: #2563eb;
      line-height: 16px;
    }
    .line {
      flex-wrap: wrap;
      margin-bottom: 20px;
      .lineItem {
        display: flex;
        align-items: center;
        width: 30%;
        margin: 16px 20px 0px 0;
        .label {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          color: #4e5969;
          line-height: 20px;
          text-align: left;
          width: 100px;
        }
        .value {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          color: #1d2129;
          line-height: 20px;
        }
      }
    }
    .tagList {
      .tag {
        margin-right: 12px;
        width: fit-content;
        border-radius: 4px 4px 4px 4px;
        padding: 2px 8px;
        box-sizing: border-box;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        background: #6b72801a;
        color: #6b7280;
      }
      .tag_blu {
        background: #e6f4ff;
        color: #1677ff;
      }
      .tag_gre {
        background: #f6ffed;
        color: #52c41a;
      }
      .tag_org {
        background: #fff7e6;
        color: #fa8c16;
      }
    }
  }
}
.divider {
  width: 100%;
  height: 2px;
  background-color: #e5e6eb;
  margin: 20px 0 28px 0;
}
</style>
