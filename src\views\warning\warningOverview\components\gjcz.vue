<template>
  <div style="width: 100%; height: 223px" id="gjczsc"></div>
</template>

<script>
import * as echarts from "echarts";
export default {
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  mounted() {},
  watch: {
    data: {
      handler(val) {
        this.initChart(val);
      },
      deep: true,
    },
  },
  methods: {
    initChart(data) {
      let total = 0;
      let resdata = data.map((x) => {
        return {
          name: x.name,
          value: x.num,
        };
      });
      data.forEach((x) => {
        total += parseInt(x.num);
      });
      let chart = echarts.init(document.getElementById("gjczsc"));
      let option = {
        color: ["#3AA1FF", "#FFCA3A", "#FF7D00", "#FF3A3A"],
        title: {
          text: total,
          subtext: "总数",
          top: "28%",
          left: "center",
          textStyle: {
            fontSize: 24,
            lineHeight: 24,
          },
          subtextStyle: {
            fontSize: 16,
          },
        },
        tooltip: {
          trigger: "item",
        },
        legend: {
          right: "12%",
          top: "bottom",
          icon: "circle",
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 18,
          textStyle: {
            padding: [0, 0, 0, 6],
          },
        },
        series: [
          {
            type: "pie",
            data: data,
            radius: ["48%", "70%"],
            center: ["50%", "40%"],
            label: {
              show: true,
              position: "outside", // 将标签放置在扇区外部
              formatter: function(params) {
                // 当value为0时只显示0%，否则使用默认格式
                return params.value === 0 ? "0%" : params.percent.toFixed(0) + "%";
              },
            },
            labelLine: {
              show: true,
              length: 10, // 连接线长度
              length2: 14, // 引导线长度
            },
            data: resdata,
          },
        ],
      };
      chart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
