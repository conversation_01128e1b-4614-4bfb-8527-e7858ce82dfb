<template>
  <div class="container">
    <div class="timeSelector flex-c">
      <div>统计日期</div>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        size="mini"
      >
      </el-date-picker>
    </div>

    <div class="flex-b">
      <div class="card card1" style="flex: 1; margin-right: 12px">
        <div class="cardTitle">安全事件数量</div>
        <div class="flex-c">
          <div class="item flex-b" v-for="(x, i) in aqsjList" :key="i">
            <div class="flex-c">
              <img :src="x.icon" class="icon" />
              <div class="name">{{ x.name }}</div>
            </div>
            <div class="num">{{ x.num }}</div>
          </div>
        </div>
      </div>
      <div class="card card2" style="flex: 1">
        <div class="cardTitle">威胁等级</div>
        <div class="flex-c">
          <div class="item flex-b" v-for="(x, i) in wxdjList" :key="i">
            <div class="flex-c">
              <img :src="x.icon" class="icon" />
              <div class="name">{{ x.name }}</div>
            </div>
            <div class="num">{{ x.num }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="card">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        label-width="100px"
        class="queryForm"
      >
        <el-form-item label="安全事件名称" prop="aqsjmc">
          <el-input
            v-model="queryParams.aqsjmc"
            placeholder="请输入"
            clearable
            style="width: 160px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="安全事件等级" prop="aqsjdj">
          <el-select
            v-model="queryParams.aqsjdj"
            placeholder="请选择"
            clearable
            style="width: 160px"
          >
            <el-option
              v-for="(item, i) in aqsjdjOptions"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="受害者" prop="shz">
          <el-select
            v-model="queryParams.shz"
            placeholder="请选择"
            clearable
            style="width: 160px"
          >
            <el-option
              v-for="(item, i) in shzOptions"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin-left: 30px">
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">
            查询
          </el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="card">
      <div class="flex-c">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="handleAdd"
          >新增</el-button
        >
        <el-button size="small" @click="handleExport">导出</el-button>
      </div>
      <el-table :data="datalist" style="margin-top: 20px">
        <el-table-column prop="aqsjmc" label="安全事件名称" align="center" />
        <el-table-column prop="aqsjdj" label="安全事件等级" align="center">
          <template slot-scope="scope">
            <div class="flex-c-c">
              <div
                class="tag flex-c-c"
                :class="
                  scope.row.aqsjdj == '高'
                    ? 'tag_red'
                    : scope.row.aqsjdj == '中'
                    ? 'tag_org'
                    : scope.row.aqsjdj == '低'
                    ? 'tag_pur'
                    : ''
                "
              >
                {{ scope.row.aqsjdj }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="shz" label="受害者" align="center" />
        <el-table-column prop="sjbq" label="事件标签" align="center" />
        <el-table-column prop="dwmc" label="单位名称" align="center" />
        <el-table-column prop="dlwz" label="地理位置" align="center" />
        <el-table-column prop="qssj" label="起始时间" align="center" />
        <el-table-column prop="gxsj" label="更新时间" align="center" />
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button @click="handleDetail(scope.row)" type="text">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
        layout="total, prev, pager, next"
        :background="false"
      />
    </div>

    <!-- 弹窗 -->
    <editDialog
      :title="title"
      :show="show"
      :form="form"
      :disabled="disabled"
      @close="close"
    ></editDialog>
  </div>
</template>

<script>
import editDialog from "@/views/warning/security/components/dialog.vue";
export default {
  components: {
    editDialog,
  },
  data() {
    return {
      dateRange: [],
      aqsjList: [
        {
          name: "当日数量",
          num: 0,
          icon: require("@/assets/images/warning/aqsj_drsl.png"),
        },
        {
          name: "未解决数量",
          num: 2,
          icon: require("@/assets/images/warning/aqsj_wjjsl.png"),
        },
      ],
      wxdjList: [
        {
          name: "低",
          num: 0,
          icon: require("@/assets/images/warning/wxdj_d.png"),
        },
        {
          name: "中",
          num: 2,
          icon: require("@/assets/images/warning/wxdj_z.png"),
        },
        {
          name: "高",
          num: 2,
          icon: require("@/assets/images/warning/wxdj_g.png"),
        },
      ],
      queryParams: {
        pageSize: 10,
        pageNum: 1,
        aqsjmc: "",
        aqsjdj: "",
        shz: "",
      },
      aqsjdjOptions: [],
      shzOptions: [],
      total: 10,
      datalist: [
        {
          aqsjmc: "网络攻击事件",
          aqsjdj: "低",
          shz: "磐安县人力社保局",
          sjbq: "网络攻击",
          dwmc: "磐安县人力社保局",
          dlwz: "地址",
          qssj: "2025-05-22 00:00:00",
          gxsj: "2025-05-27 17:15:37",
        },
        {
          aqsjmc: "网络攻击事件",
          aqsjdj: "低",
          shz: "磐安县人力社保局",
          sjbq: "网络攻击",
          dwmc: "磐安县人力社保局",
          dlwz: "地址",
          qssj: "2025-05-22 00:00:00",
          gxsj: "2025-05-27 17:15:37",
        },
        {
          aqsjmc: "网络攻击事件",
          aqsjdj: "低",
          shz: "磐安县人力社保局",
          sjbq: "网络攻击",
          dwmc: "磐安县人力社保局",
          dlwz: "地址",
          qssj: "2025-05-22 00:00:00",
          gxsj: "2025-05-27 17:15:37",
        },
      ],
      //弹窗
      title: "",
      show: false,
      form: {},
      disabled: false,
    };
  },
  mounted() {
    this.getCurrentMonth();
    this.getList();
  },
  methods: {
    getList() {
      // console.log("Search keyword:", this.searchKeyword);
    },
    handleAdd() {
      this.title = "新增安全事件";
      this.show = true;
    },
    handleExport() {},
    handleDetail(row) {
      this.title = "编辑安全事件";
      this.show = true;
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        aqsjmc: "",
        aqsjdj: "",
        shz: "",
      };
    },
    close() {
      this.show = false;
    },
    getCurrentMonth() {
      let dateStart = new Date().setDate(1);
      let month = new Date().getMonth();
      let dateEnd = new Date(new Date().setMonth(month + 1)).setDate(0);
      this.dateRange = [dateStart, dateEnd];
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  .card {
    width: 100%;
    border-radius: 15px;
    padding: 20px 20px;
    box-sizing: border-box;
    background-color: #fff;
    height: auto;
    margin-bottom: 12px;
  }
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-right: 30px;
    margin-bottom: 15px;
  }
}
.timeSelector {
  position: absolute;
  right: 20px;
  top: -40px;
  justify-content: flex-end;
  & > div {
    margin-right: 12px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 14px;
    color: #1d2129;
    line-height: 22px;
    text-align: left;
  }
}
.card1,
.card2 {
  .item {
    margin-right: 20px;
    flex: 1;
    padding: 10px 20px;
    box-sizing: border-box;
    border-radius: 10px;
    .icon {
      width: 40px;
      height: 40px;
      margin-right: 10px;
    }
    .name {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 16px;
      color: #4e5969;
      line-height: 28px;
      text-align: left;
    }
    .num {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 700;
      font-size: 28px;
      color: #1d2129;
      line-height: 42px;
      text-align: left;
    }
  }
}
.card1 {
  .item {
    &:nth-child(1) {
      background: linear-gradient(180deg, #f2f9fe 0%, #e6f4fe 100%);
    }
    &:nth-child(2) {
      background: linear-gradient(180deg, #fff8f1 0%, #ffefe7 100%);
    }
    &:last-child {
      margin-right: 0;
    }
  }
}
.card2 {
  .item {
    &:nth-child(1) {
      background: linear-gradient(180deg, #f6f7ff 0%, #ececff 100%);
    }
    &:nth-child(2) {
      background: linear-gradient(180deg, #fff8f1 0%, #ffefe7 100%);
    }
    &:nth-child(3) {
      background: linear-gradient(180deg, #fff1f1 0%, #ffdede 100%);
    }
    &:last-child {
      margin-right: 0;
    }
  }
}
.queryForm {
  ::v-deep .el-form-item {
    margin-bottom: 0;
  }
}
.tag {
  width: 52px;
  height: 28px;
  border-radius: 20px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
}
.tag_pur {
  background: #9900ff1a;
  color: #9900ff;
}
.tag_org {
  background: #ff7d001a;
  color: #ff7d00;
}
.tag_red {
  background: #ff2a001a;
  color: #ff2a00;
}
</style>
