<template>
  <div class="chart-wrapper">
    <div class="chart-title">工单完成数量</div>
    <div id="chart"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  name: 'lineChart',
  props: {
    chartData: {
      required: true,
    },
    close: {
      type: <PERSON><PERSON><PERSON>,
    }
  },
  data() {
    return {
      chart: null,
      option: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['已完成'],
          //align: 'left',
          right: 10,
          textStyle: {
            color: "#667085",
            fontSize: 14
          },
          itemWidth: 14,
          itemHeight: 10,
          itemGap: 13,
        },
        grid: {
          left: '8%',
          right: '4%',
          bottom: '14%',
          top: '18%',
        },
        xAxis: {
          type: 'category',
          data: ['6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          axisTick: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              color: '#DEE3E9'
            }
          },
          axisLabel: {//x轴文字的配置
            show: true,
            textStyle: {
              color: "#667085",
              fontSize: 14
            }
          },
        },
        yAxis: {
          type: 'value',
          name:"单位：个",//y轴上方的单位
          splitLine: {//分割线配置
            show: true,
            lineStyle: {
              color: "rgba(222,227,233,0.3)",
            }
          },
          axisLabel: {//x轴文字的配置
            show: true,
            textStyle: {
              color: "#667085",
              fontSize: 14
            }
          },
        },
        series: [
          {
            name: '已完成',
            type: 'line',
            data: [150, 230, 224, 218, 135, 147, 260],
            symbol: 'square',
            symbolSize: 0,
            itemStyle: {
              color: '#3BA1FF'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {offset: 0, color: 'rgb(59,161,255)'},
                {offset: 1, color: 'rgba(255,255,255, 0.5)'},
              ])
            }
          }
        ]
      }
    }
  },
  mounted() {
    let that = this
    setTimeout(() => {
      that.initChart()
    })
  },
  watch: {
    // 随dialog销毁组件
    close(newValue, oldValue) {
      if(!newValue) {
        this.chart.dispose()
      }
    }
  },
  methods: {
    initChart() {
      let xLabel = []
      let yLabel = []
      this.chartData.map(item => {
        xLabel.push(item.date)
        yLabel.push(item.count)
      })
      this.option.xAxis.data = xLabel
      this.option.series[0].data = yLabel
      this.chart = echarts.init(document.getElementById('chart'))
      this.chart.setOption(this.option)
    }
  }
}
</script>

<style scoped lang="scss">
.chart-title {
  font-weight: 400;
  font-size: 18px;
  color: #1D2129;
  position: relative;
  padding-left: 10px;
}
.chart-title:before {
  content: '';
  display: block;
  border-radius: 3px;
  width: 3px;
  height: 13px;
  background: #0057FE;
  left: 0px;
  top: 6px;
  position: absolute;
}
#chart {
  width: 550px;
  height: 200px;
  margin-top: 10px;
}
</style>
