<!--
 * @Description:
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-07-10 08:57:30
 * @LastEditors: wjb
 * @LastEditTime: 2025-10-30 08:56:54
-->
<template>
  <div class="container">
    <div class="card">
      <div class="flex-c">
        <div class="cardTitle">工单列表</div>
      </div>
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane label="应用工单" name="first"></el-tab-pane>
        <el-tab-pane label="项目工单" name="second"></el-tab-pane>
      </el-tabs>
      <applicationOrder v-if="activeName === 'first'"></applicationOrder>
      <projectOrder v-if="activeName === 'second'"></projectOrder>
    </div>
  </div>
</template>

<script>
import projectOrder from "./projectOrderList.vue";
import applicationOrder from "./applicationOrderList.vue";
import cache from "@/plugins/cache";

export default {
  name: "WorkorderOrderlist", // 添加组件名称，用于 keep-alive 缓存
  components: { applicationOrder, projectOrder },
  dicts: [],
  data() {
    return {
      activeName: "first",
      cacheKey: "workorder-orderlist-state", // 缓存键名
    };
  },
  created() {
    // 页面创建时恢复tab状态
    this.restoreTabState();
  },
  activated() {
    // 组件激活时（从其他页面返回时）恢复tab状态
    this.restoreTabState();
  },
  deactivated() {
    // 组件失活时（跳转到其他页面时）保存tab状态
    this.saveTabState();
  },
  beforeDestroy() {
    // 组件销毁前保存tab状态
    this.saveTabState();
  },
  methods: {
    handleClick() {
      // tab切换时保存状态
      this.saveTabState();
    },
    // 保存tab状态到缓存
    saveTabState() {
      const state = {
        activeName: this.activeName,
        timestamp: Date.now(),
      };
      cache.session.setJSON(this.cacheKey, state);
    },
    // 从缓存恢复tab状态
    restoreTabState() {
      const cachedState = cache.session.getJSON(this.cacheKey);
      if (cachedState && cachedState.timestamp) {
        // 如果缓存时间在30分钟内，则恢复状态
        const now = Date.now();
        if (now - cachedState.timestamp < 30 * 60 * 1000) {
          if (cachedState.activeName) {
            this.activeName = cachedState.activeName;
          }
        }
      }
    },
    // 清除tab状态缓存
    clearTabState() {
      cache.session.remove(this.cacheKey);
    },
  },
};
</script>

<style lang="scss" scoped>
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-bottom: 10px;
    padding-left: 18px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 2px;
      left: 0;
      width: 10px;
      height: 20px;
      background: url("~@/assets/images/cardTitle_icon.png");
      background-size: 100% 100%;
    }
  }
}
</style>