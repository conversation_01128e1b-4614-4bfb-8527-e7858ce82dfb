<template>
  <div class="flex-b" style="align-items: flex-start">
    <!-- 左侧事件处理记录 -->
    <div class="card left">
      <div class="flex-c">
        <div class="cardTitle">事件处理记录</div>
        <el-button icon="el-icon-plus" size="mini" class="btn">新增</el-button>
      </div>
      <div class="con">
        <div>
          <el-table :data="datalist" style="margin-top: 10px" ref="myTable">
            <el-table-column prop="sjbh" label="事件编号" align="center" />
            <el-table-column prop="lx" label="类型" align="center" />
            <el-table-column prop="clr" label="处理人" align="center" />
            <el-table-column prop="bc" label="班次" align="center">
              <template slot-scope="scope">
                <div class="flex-c-c">
                  <div
                    class="tag flex-c-c"
                    :class="
                      scope.row.bc == '处理中'
                        ? 'tag_yel'
                        : scope.row.bc == '待处理'
                        ? 'tag_org'
                        : scope.row.bc == '已处理'
                        ? 'tag_gre'
                        : ''
                    "
                  >
                    {{ scope.row.bc || "-" }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="jjcd" label="紧急程度" align="center">
              <template slot-scope="scope">
                <div class="flex-c-c">
                  <div
                    class="tag flex-c-c"
                    :class="
                      scope.row.jjcd == '高'
                        ? 'tag_org'
                        : scope.row.jjcd == '中'
                        ? 'tag_yel'
                        : scope.row.jjcd == '紧急'
                        ? 'tag_red'
                        : ''
                    "
                  >
                    {{ scope.row.jjcd || "-" }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  @click="handleDetail(scope.$index, scope.row)"
                >
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <pagination
          :background="false"
          layout="total, prev, pager, next"
          v-show="total > 0"
          :total="total"
          :page.sync="params.pageNum"
          :limit.sync="params.pageSize"
          @pagination="getList"
          :auto-scroll="false"
        />
      </div>
    </div>

    <!-- 右侧值班交接 -->
    <div class="card right">
      <div class="flex-c">
        <div class="cardTitle">值班交接</div>
        <el-button icon="el-icon-plus" size="mini" class="btn">新增</el-button>
      </div>

      <!-- 新增时间线组件 -->
      <el-timeline class="timeline">
        <el-timeline-item
          v-for="(item, index) in handoverLogs"
          :key="index"
          :timestamp="`${item.time}\n${item.date}`"
          placement="top"
          color="#165DFF"
        >
          <div class="handoverInfo">
            <div class="title">遗留问题:</div>
            <div
              class="value"
              v-for="(x, i) in item.leftoverProblems"
              :key="i + '1'"
            >
              ·{{ x }}
            </div>
            <div class="title">注意事项:</div>
            <div class="value" v-for="(x, i) in item.notes" :key="i + '2'">
              ·{{ x }}
            </div>
            <div class="title">交接人员:</div>
            <div class="value">{{ item.handoverPerson }}</div>
            <div class="title">交接时间:</div>
            <div class="value">{{ item.handoverTime }}</div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      params: {
        pageNum: 1,
        pageSize: 10,
      },
      datalist: [
        {
          sjbh: "EV20231001",
          lx: "系统告警",
          clr: "李娜",
          bc: "处理中",
          jjcd: "高",
        },
        {
          sjbh: "EV20231001",
          lx: "系统告警",
          clr: "李娜",
          bc: "待处理",
          jjcd: "中",
        },
        {
          sjbh: "EV20231001",
          lx: "系统告警",
          clr: "李娜",
          bc: "已处理",
          jjcd: "紧急",
        },
        {
          sjbh: "EV20231001",
          lx: "系统告警",
          clr: "李娜",
          bc: "处理中",
          jjcd: "高",
        },
        {
          sjbh: "EV20231001",
          lx: "系统告警",
          clr: "李娜",
          bc: "处理中",
          jjcd: "高",
        },
        {
          sjbh: "EV20231001",
          lx: "系统告警",
          clr: "李娜",
          bc: "处理中",
          jjcd: "高",
        },
      ],
      total: 200,
      handoverLogs: [
        {
          time: "14:00:00",
          date: "2025-06-13",
          leftoverProblems: [
            "数据库查询优化任务，已通知开发团队，预计明天完成",
            "监控系统日志存储空间问题，已提交工单，等待处理",
          ],
          notes: [
            "明天10:00-12:00计划进行服务器例行维护",
            "关注业务高峰期服务器负载情况",
            "保持与开发团队的沟通，跟进问题解决进度",
          ],
          handoverPerson: "王华",
          handoverTime: "2025-6-13 09:31:16",
        },
        {
          time: "14:00:00",
          date: "2025-06-13",
          leftoverProblems: [
            "数据库查询优化任务，已通知开发团队，预计明天完成",
            "监控系统日志存储空间问题，已提交工单，等待处理",
          ],
          notes: [
            "明天10:00-12:00计划进行服务器例行维护",
            "关注业务高峰期服务器负载情况",
            "保持与开发团队的沟通，跟进问题解决进度",
          ],
          handoverPerson: "王华",
          handoverTime: "2025-6-13 09:31:16",
        },
      ],
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList() {},
    handleDetail() {},
  },
};
</script>

<style lang="scss" scoped>
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-bottom: 10px;
    padding-left: 18px;
    box-sizing: border-box;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      top: 2px;
      left: 0;
      width: 10px;
      height: 20px;
      background: url("~@/assets/images/cardTitle_icon.png");
      background-size: 100% 100%;
    }
  }
}
.btn {
  margin-bottom: 6px;
  margin-left: 20px;
}
.left,
.right {
  flex: 1;
}
.left {
  margin-right: 12px;
  .con {
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .tag {
    width: 60px;
    height: 28px;
    border-radius: 20px;
    font-size: 14px;
    line-height: 20px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
  }
  .tag_yel {
    background-color: #ffca3a1a;
    color: #ffca3a;
  }
  .tag_org {
    background-color: #ff7d001a;
    color: #ff7d00;
  }
  .tag_gre {
    background-color: #36cbcb1a;
    color: #36cbcb;
  }
  .tag_red {
    background-color: #ff00001a;
    color: #ff0000;
  }
}
.right {
  .timeline {
    width: auto;
    margin-left: 80px;
    box-sizing: border-box;
    margin-top: 20px;
    .handoverInfo {
      background: #f9fafb;
      border-radius: 15px 15px 15px 15px;
      border: 1px solid #eff0f1;
      padding: 15px 18px;
      box-sizing: border-box;
      .title {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 700;
        font-size: 14px;
        color: #1d2129;
        line-height: 21px;
        text-align: left;
        margin: 10px 0 2px 0;
        &:first-child {
          margin-top: 0;
        }
      }
      .value {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 12px;
        color: #667085;
        line-height: 20px;
        text-align: left;
        word-break: break-all;
      }
    }
  }
  .el-timeline {
    padding-left: 0;
    ::v-deep.el-timeline-item__wrapper {
      position: relative;
    }
    ::v-deep .el-timeline-item__timestamp {
      position: absolute;
      left: -94px;
      top: -4px;
      white-space: normal; /* 允许文本换行 */
      width: 80px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      color: #1d2129;
      line-height: 20px;
      text-align: right;
    }
    ::v-deep .el-timeline-item__tail {
      top: 8px;
      border-left-color: #165dff;
    }
  }
}
</style>
