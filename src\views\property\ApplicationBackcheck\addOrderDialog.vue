<template>
  <el-dialog
    :visible.sync="showFlag"
    title="创建新工单"
    width="700px"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <div style="display: flex; justify-content: center">
      <el-form
        :model="form"
        :rules="rules"
        ref="form"
        label-position="left"
        label-width="100px"
      >
        <el-form-item label="工单类型" prop="gdType">
          <el-select v-model="form.gdType" style="width: 100%">
            <el-option
              v-for="dict in dict.type.gdlx"
              :key="dict.value"
              :value="dict.value"
              :label="dict.label"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工单标题" prop="title">
          <el-input
            v-model="form.title"
            placeholder="工单标题"
            :maxlength="60"
          />
        </el-form-item>
        <el-form-item label="所属部门">
          <el-input disabled v-model="deptName" placeholder="所属部门" />
        </el-form-item>
        <el-form-item label="所属应用" prop="yyId">
          <el-select
            v-model="form.yyId"
            placeholder="所属应用"
            clearable
            disabled
            multiple
            style="width: 100%"
          >
            <el-option
              v-for="(item, i) in appOptions"
              :key="i"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="describe">
          <el-input
            v-model="form.describe"
            placeholder="描述"
            type="textarea"
            :rows="2"
          />
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="form.priority" style="width: 100%">
            <el-option
              v-for="(item, i) in priorityOptions"
              :key="i"
              :value="item.value"
              :label="item.label"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="处理人" prop="handlerId">
          <el-select v-model="form.handlerId" style="width: 100%">
            <el-option
              v-for="(item, i) in clrOptions"
              :key="i"
              :value="item.value"
              :label="item.label"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="抄送人" prop="csList">
          <el-select
            v-model="form.csList"
            multiple
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="(item, i) in csrOptions"
              :key="i"
              :value="item.value"
              :label="item.label"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="附件">
          <FileUpload :limit="5" v-model="fjList" style="flex: 1"></FileUpload>
        </el-form-item>
        <div class="footer">
          <el-button type="primary" @click="submit"> 确 定 </el-button>
          <el-button @click="cancel"> 取 消 </el-button>
        </div>
      </el-form>
    </div>
  </el-dialog>
</template>

<script>
import FileUpload from "@/components/FileUpload/index.vue";
import { listAllYy, listClry, getUsercsList } from "@/api/serve/orderlist";
import { wlsdAdd } from "@/api/property/supplyChain";
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    deptName1: {
      type: String,
      default: "",
    },
    yyId1: {
      type: Array,
      default: () => [],
    },
  },
  components: { FileUpload },
  dicts: ["gdlx"],
  data() {
    return {
      showFlag: false,
      appOptions: [],
      priorityOptions: [
        { label: "低", value: 1 },
        { label: "中", value: 2 },
        { label: "高", value: 3 },
      ],
      clrOptions: [],
      csrOptions: [],
      fjList: [],
      form: {
        gdType: "",
        title: "",
        describe: "",
        priority: "",
        handlerId: "",
        yyId: [],
        csList: [],
      },
      deptName: "",
      rules: {
        gdType: [{ required: true, message: "请选择", trigger: "change" }],
        yyId: [{ required: true, message: "请选择", trigger: "change" }],
        title: [{ required: true, message: "请输入", trigger: "blur" }],
        describe: [{ required: true, message: "请输入", trigger: "blur" }],
        priority: [{ required: true, message: "请选择", trigger: "change" }],
        handlerId: [{ required: true, message: "请选择", trigger: "change" }],
      },
    };
  },
  watch: {
    show: function (val) {
      this.showFlag = val;
      if (this.showFlag) {
        this.initData();
        this.deptName = this.deptName1;
        this.form.yyId = this.yyId1;
      }
    },
  },
  mounted() {},
  methods: {
    reset() {
      this.form = {
        gdType: "",
        title: "",
        describe: "",
        priority: "",
        yyId: [],
        handlerId: "",
        csList: [],
      };
      this.fjList = [];
      this.resetForm("form");
    },
    cancel() {
      this.reset();
      this.showFlag = false;
      this.$emit("close");
    },
    initData() {
      this.reset();
      listAllYy().then((res) => {
        this.appOptions = res.data;
      });
      listClry().then((res) => {
        this.clrOptions = res.data.map((item) => {
          return {
            label: item.userName,
            value: item.userId,
          };
        });
      });
      getUsercsList().then((res) => {
        this.csrOptions = res.rows.map((item) => {
          return {
            label: item.nickName,
            value: item.userId,
          };
        });
      });
    },
    //新增工单开始--
    submit() {
      // 处理附件列表，按照文件后缀分类
      const processedFiles = this.processFilesByExtension(this.fjList);

      // 将fileName字段拼接成逗号分隔的字符串
      const fileUrl = processedFiles.map((file) => file.fileName).join(",");

      // 将type字段拼接成逗号分隔的字符串
      const fileSuffix = processedFiles.map((file) => file.type).join(",");

      const scList = this.form.csList.map((item) => ({ userId: item }));

      console.log(scList, 111);
      this.$refs.form.validate((valid) => {
        if (valid) {
          let params = {
            gdType: this.form.gdType,
            title: this.form.title,
            describe: this.form.describe,
            priority: this.form.priority,
            yyId: this.form.yyId,
            handlerId: this.form.handlerId,
            dataId: this.form.dataId,
            dataType: this.form.dataType,
            fileUrl: fileUrl,
            fileSuffix: fileSuffix,
            csList: scList,
          };
          wlsdAdd(params).then((response) => {
            this.$message.success("新增成功");
            this.showFlag = true;
            this.$emit("addSuccess");
          });
        }
      });
    },
    /**
     * 根据文件后缀处理文件列表
     * @param {String|Array} fileList - 文件列表，可能是逗号分隔的字符串或对象数组
     * @returns {Array} 处理后的文件数组，每个文件包含 fileName 和 type 字段
     */
    processFilesByExtension(fileList) {
      if (!fileList) return [];

      let files = [];

      // 如果是字符串，按逗号分割
      if (typeof fileList === "string") {
        files = fileList.split(",").filter((file) => file.trim());
      }
      // 如果是数组
      else if (Array.isArray(fileList)) {
        files = fileList
          .map((item) => {
            // 如果是对象，取 name 或 url 字段
            if (typeof item === "object" && item !== null) {
              return item.name || item.url || "";
            }
            // 如果是字符串，直接返回
            return item || "";
          })
          .filter((file) => file.trim());
      }

      // 处理每个文件，根据后缀确定类型
      return files.map((fileName) => {
        const extension = this.getFileExtension(fileName);
        const type = this.getFileTypeByExtension(extension);

        return {
          fileName: fileName,
          type: type,
          extension: extension,
        };
      });
    },

    /**
     * 获取文件扩展名
     * @param {String} fileName - 文件名
     * @returns {String} 文件扩展名（小写）
     */
    getFileExtension(fileName) {
      if (!fileName || typeof fileName !== "string") return "";

      const lastDotIndex = fileName.lastIndexOf(".");
      if (lastDotIndex === -1) return "";

      return fileName.substring(lastDotIndex + 1).toLowerCase();
    },

    /**
     * 根据文件扩展名获取文件类型
     * @param {String} extension - 文件扩展名
     * @returns {Number} 文件类型：1-文档，2-表格，3-PDF，4-图片
     */
    getFileTypeByExtension(extension) {
      const typeMap = {
        // 文档类型 - type: 1
        doc: 1,
        docx: 1,

        // 表格类型 - type: 2
        xls: 2,
        xlsx: 2,

        // PDF类型 - type: 3
        pdf: 3,

        // 图片类型 - type: 4
        png: 4,
        jpg: 4,
        jpeg: 4,
      };

      return typeMap[extension] || 0; // 未知类型返回 0
    },
  },
};
</script>

<style lang="scss" scoped>
.info {
  padding: 20px;
  box-sizing: border-box;
  .line {
    display: table;
    width: 100%;
    table-layout: fixed;
    &:last-child {
      border-bottom: 1px solid #eff0f1;
    }
    .label {
      display: table-cell;
      width: 200px;
      background: #f9fafb;
      border: 1px solid #eff0f1;
      border-bottom: none;
      border-right: none;
      box-sizing: border-box;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      vertical-align: top;
      padding: 8px 21px;
    }
    .value {
      display: table-cell;
      vertical-align: top;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      color: #667085;
      border: 1px solid #eff0f1;
      border-bottom: none;
      text-align: left;
      box-sizing: border-box;
      padding: 8px 21px;
      background: #fff;
      word-break: break-all;

      // 处理记录表格样式
      .processing-records {
        margin-top: 8px;

        .el-table {
          font-size: 12px;

          .el-table__header {
            th {
              background-color: #f5f7fa;
              color: #606266;
              font-weight: 500;
            }
          }

          .el-table__body {
            tr:hover > td {
              background-color: #f5f7fa;
            }
          }

          .el-button--text {
            color: #409eff;
            padding: 0;
            font-size: 12px;

            &:hover {
              color: #66b1ff;
            }
          }
        }

        // 附件按钮样式
        .annex-buttons {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .annex-btn {
            text-align: left;
            justify-content: flex-start;
            padding: 2px 4px;
            margin: 0;
            min-height: auto;
            line-height: 1.2;

            &:hover {
              background-color: #ecf5ff;
              border-radius: 2px;
            }
          }
        }
      }
    }
  }
}
::v-deep .el-dialog {
  border-radius: 12px;
  overflow: hidden;
  .el-dialog__body {
    padding: 0;
    max-height: 960px;
    overflow-y: scroll;
    padding-bottom: 20px;
    margin-bottom: 20px;
  }
}
::v-deep .el-form-item__content {
  width: 480px;
}
</style>
